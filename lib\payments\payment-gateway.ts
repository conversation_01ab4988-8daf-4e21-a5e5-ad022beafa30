/**
 * 支付网关核心抽象
 * 统一的支付接口管理
 */

import type {
  PaymentProvider,
  PaymentMethod,
  CreatePaymentParams,
  PaymentResult,
  PaymentQueryResult,
  CallbackVerifyResult,
  PAYMENT_METHOD_CONFIG
} from '@/types/payment'

// 支付网关管理器
export class PaymentGateway {
  private providers: Map<PaymentMethod, PaymentProvider> = new Map()
  private defaultProvider: PaymentMethod = 'qiling_pay'

  constructor() {
    this.initializeProviders()
  }

  // 初始化支付提供商
  private async initializeProviders() {
    // 动态导入支付提供商
    try {
      const { QilingPayProvider } = await import('./qiling-payment')
      this.registerProvider('qiling_pay', new QilingPayProvider())
    } catch (error) {
      console.error('Failed to load QilingPayProvider:', error)
    }

    // 支付宝支付
    try {
      const { AlipayProvider } = await import('./alipay-provider')
      this.registerProvider('alipay', new AlipayProvider())
    } catch (error) {
      console.error('Failed to load AlipayProvider:', error)
    }

    // 微信支付
    try {
      const { WechatPayProvider } = await import('./wechat-provider')
      this.registerProvider('wechat_pay', new WechatPayProvider())
    } catch (error) {
      console.error('Failed to load WechatPayProvider:', error)
    }
  }

  // 注册支付提供商
  registerProvider(method: PaymentMethod, provider: PaymentProvider) {
    this.providers.set(method, provider)
  }

  // 获取支付提供商
  getProvider(method: PaymentMethod): PaymentProvider | null {
    return this.providers.get(method) || null
  }

  // 获取可用的支付方式
  getAvailablePaymentMethods(): PaymentMethod[] {
    const availableMethods: PaymentMethod[] = []
    
    for (const [method, provider] of this.providers) {
      if (provider.isEnabled) {
        availableMethods.push(method)
      }
    }

    // 添加现金支付（总是可用）
    if (!availableMethods.includes('cash')) {
      availableMethods.push('cash')
    }

    return availableMethods
  }

  // 创建支付
  async createPayment(
    method: PaymentMethod,
    params: CreatePaymentParams
  ): Promise<PaymentResult> {
    try {
      // 现金支付特殊处理
      if (method === 'cash') {
        return this.createCashPayment(params)
      }

      const provider = this.getProvider(method)
      if (!provider) {
        return {
          success: false,
          error: `不支持的支付方式: ${method}`,
        }
      }

      if (!provider.isEnabled) {
        return {
          success: false,
          error: `支付方式 ${method} 暂时不可用`,
        }
      }

      return await provider.createPayment(params)
    } catch (error) {
      console.error('Create payment error:', error)
      return {
        success: false,
        error: '创建支付失败，请稍后重试',
      }
    }
  }

  // 查询支付状态
  async queryPayment(
    method: PaymentMethod,
    paymentId: string
  ): Promise<PaymentQueryResult> {
    try {
      // 现金支付特殊处理
      if (method === 'cash') {
        return this.queryCashPayment(paymentId)
      }

      const provider = this.getProvider(method)
      if (!provider) {
        return {
          success: false,
          status: 'failed',
          error: `不支持的支付方式: ${method}`,
        }
      }

      return await provider.queryPayment(paymentId)
    } catch (error) {
      console.error('Query payment error:', error)
      return {
        success: false,
        status: 'failed',
        error: '查询支付状态失败',
      }
    }
  }

  // 取消支付
  async cancelPayment(
    method: PaymentMethod,
    paymentId: string
  ): Promise<PaymentResult> {
    try {
      // 现金支付特殊处理
      if (method === 'cash') {
        return this.cancelCashPayment(paymentId)
      }

      const provider = this.getProvider(method)
      if (!provider) {
        return {
          success: false,
          error: `不支持的支付方式: ${method}`,
        }
      }

      return await provider.cancelPayment(paymentId)
    } catch (error) {
      console.error('Cancel payment error:', error)
      return {
        success: false,
        error: '取消支付失败',
      }
    }
  }

  // 退款
  async refundPayment(
    method: PaymentMethod,
    paymentId: string,
    amount?: number
  ): Promise<PaymentResult> {
    try {
      // 现金支付特殊处理
      if (method === 'cash') {
        return this.refundCashPayment(paymentId, amount)
      }

      const provider = this.getProvider(method)
      if (!provider) {
        return {
          success: false,
          error: `不支持的支付方式: ${method}`,
        }
      }

      return await provider.refundPayment(paymentId, amount)
    } catch (error) {
      console.error('Refund payment error:', error)
      return {
        success: false,
        error: '退款失败',
      }
    }
  }

  // 验证回调
  async verifyCallback(
    method: PaymentMethod,
    data: any
  ): Promise<CallbackVerifyResult> {
    try {
      const provider = this.getProvider(method)
      if (!provider) {
        return {
          success: false,
          error: `不支持的支付方式: ${method}`,
        }
      }

      return await provider.verifyCallback(data)
    } catch (error) {
      console.error('Verify callback error:', error)
      return {
        success: false,
        error: '验证回调失败',
      }
    }
  }

  // 现金支付处理
  private async createCashPayment(params: CreatePaymentParams): Promise<PaymentResult> {
    // 现金支付直接返回成功，需要手动确认
    return {
      success: true,
      paymentId: `cash_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      message: '现金支付订单已创建，请线下完成支付',
    }
  }

  private async queryCashPayment(paymentId: string): Promise<PaymentQueryResult> {
    // 现金支付状态需要从数据库查询
    return {
      success: true,
      status: 'pending', // 默认待确认状态
    }
  }

  private async cancelCashPayment(paymentId: string): Promise<PaymentResult> {
    return {
      success: true,
      message: '现金支付已取消',
    }
  }

  private async refundCashPayment(paymentId: string, amount?: number): Promise<PaymentResult> {
    return {
      success: true,
      message: '现金退款已处理，请线下完成退款',
    }
  }

  // 获取支付方式配置
  getPaymentMethodConfig(method: PaymentMethod) {
    return PAYMENT_METHOD_CONFIG[method]
  }

  // 验证支付金额
  validateAmount(amount: number): boolean {
    return amount > 0 && amount <= 999999.99
  }

  // 生成支付订单号
  generatePaymentId(prefix: string = 'PAY'): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    return `${prefix}_${timestamp}_${random}`
  }

  // 格式化金额
  formatAmount(amount: number, currency: string = 'CNY'): string {
    const formatter = new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
    })
    return formatter.format(amount)
  }

  // 计算手续费
  calculateFee(amount: number, method: PaymentMethod): number {
    // 不同支付方式的手续费率
    const feeRates = {
      qiling_pay: 0, // 内部支付系统免手续费
      alipay: 0.006, // 0.6%
      wechat_pay: 0.006, // 0.6%
      bank_card: 0.008, // 0.8%
      cash: 0, // 现金免手续费
    }

    const rate = feeRates[method] || 0
    return Math.round(amount * rate * 100) / 100 // 保留两位小数
  }
}

// 全局支付网关实例
export const paymentGateway = new PaymentGateway()
