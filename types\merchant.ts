/**
 * 商家相关类型定义
 * 扩展数据库类型，添加业务逻辑类型
 */

import type { Database } from './database'

// 从数据库类型中提取商家类型
export type Merchant = Database['public']['Tables']['merchants']['Row']
export type MerchantInsert = Database['public']['Tables']['merchants']['Insert']
export type MerchantUpdate = Database['public']['Tables']['merchants']['Update']

// 业务类型枚举
export type BusinessType = 'hotel' | 'restaurant' | 'product'

// 商家状态枚举
export type MerchantStatus = 'active' | 'inactive' | 'suspended'

// 营业时间类型
export interface DaySchedule {
  open: string
  close: string
  isOpen: boolean
}

export interface BusinessHours {
  monday: DaySchedule
  tuesday: DaySchedule
  wednesday: DaySchedule
  thursday: DaySchedule
  friday: DaySchedule
  saturday: DaySchedule
  sunday: DaySchedule
}

// 联系信息类型
export interface ContactInfo {
  phone?: string
  email?: string
  address?: string
  website?: string
}

// 商家统计信息类型
export interface MerchantStats {
  total_orders: number
  pending_orders: number
  completed_orders: number
  total_revenue: number
  total_hotels: number
  total_restaurants: number
  total_products: number
  low_stock_products: number
}

// 商家详细信息类型（包含关联数据）
export interface MerchantWithDetails extends Merchant {
  stats?: MerchantStats
  hotels_count?: number
  restaurants_count?: number
  products_count?: number
}

// 商家表单数据类型
export interface MerchantFormData {
  name: string
  description?: string
  business_types: BusinessType[]
  contact_info: ContactInfo
  business_hours?: BusinessHours
  logo?: string
}

// 图片上传响应类型
export interface ImageUploadResponse {
  success: boolean
  url?: string
  error?: string
}

// 商家操作结果类型
export interface MerchantActionResult {
  success: boolean
  data?: Merchant
  error?: string
  message?: string
}

// 业务类型配置
export const BUSINESS_TYPE_CONFIG = {
  hotel: {
    label: '酒店管理',
    description: '管理酒店信息、房间类型和预订',
    icon: '🏨',
    color: 'blue',
  },
  restaurant: {
    label: '餐饮管理',
    description: '管理餐厅信息、菜单和订餐',
    icon: '🍽️',
    color: 'green',
  },
  product: {
    label: '商品管理',
    description: '管理商品信息、库存和销售',
    icon: '🛍️',
    color: 'purple',
  },
} as const

// 商家状态配置
export const MERCHANT_STATUS_CONFIG = {
  active: {
    label: '正常营业',
    description: '商家正常营业中',
    color: 'green',
  },
  inactive: {
    label: '暂停营业',
    description: '商家暂时停止营业',
    color: 'yellow',
  },
  suspended: {
    label: '账户冻结',
    description: '商家账户被冻结',
    color: 'red',
  },
} as const

// 星期配置
export const WEEKDAY_CONFIG = {
  monday: '周一',
  tuesday: '周二',
  wednesday: '周三',
  thursday: '周四',
  friday: '周五',
  saturday: '周六',
  sunday: '周日',
} as const

// 工具函数类型
export type WeekDay = keyof BusinessHours
