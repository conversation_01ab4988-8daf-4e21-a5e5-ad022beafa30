-- 柒零支付连锁平台种子数据
-- 创建时间: 2025-07-01
-- 描述: 用于开发和测试的示例数据

-- 注意：这些数据仅用于开发环境，生产环境请勿使用

-- 插入示例商家数据
INSERT INTO merchants (id, user_id, name, description, business_types, contact_info, business_hours, status) VALUES
(
    '550e8400-e29b-41d4-a716-446655440001',
    NULL, -- 需要在实际使用时关联真实用户
    '柒零连锁集团',
    '专业的酒店、餐饮、零售连锁服务提供商',
    ARRAY['hotel', 'restaurant', 'product']::business_type[],
    '{"phone": "************", "email": "<EMAIL>", "address": "北京市朝阳区建国门外大街1号"}',
    '{
        "monday": {"open": "00:00", "close": "23:59", "isOpen": true},
        "tuesday": {"open": "00:00", "close": "23:59", "isOpen": true},
        "wednesday": {"open": "00:00", "close": "23:59", "isOpen": true},
        "thursday": {"open": "00:00", "close": "23:59", "isOpen": true},
        "friday": {"open": "00:00", "close": "23:59", "isOpen": true},
        "saturday": {"open": "00:00", "close": "23:59", "isOpen": true},
        "sunday": {"open": "00:00", "close": "23:59", "isOpen": true}
    }',
    'active'
),
(
    '550e8400-e29b-41d4-a716-446655440002',
    NULL,
    '美食天地',
    '精品餐饮连锁品牌',
    ARRAY['restaurant']::business_type[],
    '{"phone": "************", "email": "<EMAIL>", "address": "上海市浦东新区陆家嘴环路1000号"}',
    '{
        "monday": {"open": "10:00", "close": "22:00", "isOpen": true},
        "tuesday": {"open": "10:00", "close": "22:00", "isOpen": true},
        "wednesday": {"open": "10:00", "close": "22:00", "isOpen": true},
        "thursday": {"open": "10:00", "close": "22:00", "isOpen": true},
        "friday": {"open": "10:00", "close": "23:00", "isOpen": true},
        "saturday": {"open": "10:00", "close": "23:00", "isOpen": true},
        "sunday": {"open": "10:00", "close": "22:00", "isOpen": true}
    }',
    'active'
);

-- 插入示例酒店数据
INSERT INTO hotels (id, merchant_id, name, description, address, phone, email, images, amenities, rating, total_rooms, available_rooms) VALUES
(
    '660e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440001',
    '柒零商务酒店（国贸店）',
    '位于CBD核心区域的高端商务酒店，提供优质的住宿体验',
    '北京市朝阳区建国门外大街甲6号',
    '010-85888888',
    '<EMAIL>',
    ARRAY['/images/hotels/qiling-guomao-1.jpg', '/images/hotels/qiling-guomao-2.jpg'],
    ARRAY['免费WiFi', '健身房', '商务中心', '会议室', '停车场', '24小时前台'],
    4.5,
    200,
    150
),
(
    '660e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440001',
    '柒零度假酒店（三亚店）',
    '海滨度假酒店，享受阳光沙滩的完美假期',
    '海南省三亚市亚龙湾国家旅游度假区',
    '0898-88888888',
    '<EMAIL>',
    ARRAY['/images/hotels/qiling-sanya-1.jpg', '/images/hotels/qiling-sanya-2.jpg'],
    ARRAY['海景房', '私人海滩', '游泳池', 'SPA', '儿童乐园', '免费WiFi'],
    4.8,
    150,
    80
);

-- 插入示例房间类型数据
INSERT INTO room_types (id, hotel_id, name, description, base_price, max_occupancy, size_sqm, amenities, total_rooms, available_rooms) VALUES
(
    '770e8400-e29b-41d4-a716-446655440001',
    '660e8400-e29b-41d4-a716-446655440001',
    '商务标准间',
    '舒适的商务标准间，配备现代化设施',
    588.00,
    2,
    28.0,
    ARRAY['大床', '办公桌', '免费WiFi', '空调', '电视', '迷你吧'],
    100,
    75
),
(
    '770e8400-e29b-41d4-a716-446655440002',
    '660e8400-e29b-41d4-a716-446655440001',
    '行政套房',
    '宽敞的行政套房，享受尊贵服务',
    1288.00,
    4,
    65.0,
    ARRAY['客厅', '卧室', '办公区', '免费WiFi', '空调', '电视', '迷你吧', '行政酒廊'],
    50,
    35
),
(
    '770e8400-e29b-41d4-a716-446655440003',
    '660e8400-e29b-41d4-a716-446655440002',
    '海景房',
    '面朝大海的豪华海景房',
    888.00,
    2,
    35.0,
    ARRAY['海景阳台', '大床', '免费WiFi', '空调', '电视', '迷你吧'],
    80,
    45
),
(
    '770e8400-e29b-41d4-a716-446655440004',
    '660e8400-e29b-41d4-a716-446655440002',
    '海景套房',
    '奢华海景套房，尽享海岛风情',
    1688.00,
    4,
    80.0,
    ARRAY['海景阳台', '客厅', '卧室', '按摩浴缸', '免费WiFi', '空调', '电视', '迷你吧'],
    30,
    15
);

-- 插入示例餐厅数据
INSERT INTO restaurants (id, merchant_id, name, description, address, phone, email, cuisine_types, price_range, rating, capacity) VALUES
(
    '880e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440002',
    '川香府',
    '正宗川菜，麻辣鲜香，传承经典',
    '上海市浦东新区陆家嘴环路1000号B1层',
    '021-58888888',
    '<EMAIL>',
    ARRAY['川菜', '中餐', '火锅'],
    '中等',
    4.6,
    120
),
(
    '880e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440002',
    '日式料理·樱花',
    '精致日式料理，新鲜食材，匠心制作',
    '上海市浦东新区陆家嘴环路1000号2层',
    '021-58888889',
    '<EMAIL>',
    ARRAY['日料', '寿司', '刺身'],
    '高端',
    4.7,
    80
);

-- 插入示例菜单分类数据
INSERT INTO menu_categories (id, restaurant_id, name, description, sort_order) VALUES
(
    '990e8400-e29b-41d4-a716-446655440001',
    '880e8400-e29b-41d4-a716-446655440001',
    '招牌菜',
    '川香府经典招牌菜品',
    1
),
(
    '990e8400-e29b-41d4-a716-446655440002',
    '880e8400-e29b-41d4-a716-446655440001',
    '川味小炒',
    '经典川味小炒菜品',
    2
),
(
    '990e8400-e29b-41d4-a716-446655440003',
    '880e8400-e29b-41d4-a716-446655440002',
    '寿司拼盘',
    '新鲜寿司拼盘系列',
    1
),
(
    '990e8400-e29b-41d4-a716-446655440004',
    '880e8400-e29b-41d4-a716-446655440002',
    '刺身料理',
    '精选刺身料理',
    2
);

-- 插入示例菜品数据
INSERT INTO menu_items (id, restaurant_id, category_id, name, description, price, ingredients, preparation_time, is_spicy) VALUES
(
    'aa0e8400-e29b-41d4-a716-446655440001',
    '880e8400-e29b-41d4-a716-446655440001',
    '990e8400-e29b-41d4-a716-446655440001',
    '麻婆豆腐',
    '经典川菜，嫩滑豆腐配麻辣肉末',
    38.00,
    ARRAY['豆腐', '猪肉末', '豆瓣酱', '花椒', '葱花'],
    15,
    true
),
(
    'aa0e8400-e29b-41d4-a716-446655440002',
    '880e8400-e29b-41d4-a716-446655440001',
    '990e8400-e29b-41d4-a716-446655440001',
    '宫保鸡丁',
    '酸甜微辣，鸡肉嫩滑，花生香脆',
    48.00,
    ARRAY['鸡胸肉', '花生米', '干辣椒', '花椒', '胡萝卜丁'],
    20,
    true
),
(
    'aa0e8400-e29b-41d4-a716-446655440003',
    '880e8400-e29b-41d4-a716-446655440002',
    '990e8400-e29b-41d4-a716-446655440003',
    '三文鱼寿司拼盘',
    '新鲜三文鱼寿司，8贯装',
    88.00,
    ARRAY['三文鱼', '寿司米', '海苔', '芥末', '生抽'],
    10,
    false
),
(
    'aa0e8400-e29b-41d4-a716-446655440004',
    '880e8400-e29b-41d4-a716-446655440002',
    '990e8400-e29b-41d4-a716-446655440004',
    '金枪鱼刺身',
    '新鲜金枪鱼刺身，入口即化',
    128.00,
    ARRAY['金枪鱼', '萝卜丝', '芥末', '生抽'],
    5,
    false
);

-- 插入示例商品分类数据
INSERT INTO product_categories (id, merchant_id, name, description, sort_order) VALUES
(
    'bb0e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440001',
    '酒店用品',
    '酒店客房和公共区域用品',
    1
),
(
    'bb0e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440001',
    '餐饮用品',
    '餐厅厨房和服务用品',
    2
),
(
    'bb0e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440001',
    '纪念品',
    '特色纪念品和礼品',
    3
);

-- 插入示例商品数据
INSERT INTO products (id, merchant_id, category_id, name, description, sku, price, stock_quantity, min_stock_level) VALUES
(
    'cc0e8400-e29b-41d4-a716-446655440001',
    '550e8400-e29b-41d4-a716-446655440001',
    'bb0e8400-e29b-41d4-a716-446655440001',
    '柒零酒店毛巾套装',
    '100%纯棉毛巾，柔软吸水，酒店专用',
    'QL-TOWEL-001',
    68.00,
    500,
    50
),
(
    'cc0e8400-e29b-41d4-a716-446655440002',
    '550e8400-e29b-41d4-a716-446655440001',
    'bb0e8400-e29b-41d4-a716-446655440001',
    '柒零品牌拖鞋',
    '舒适防滑拖鞋，一次性使用',
    'QL-SLIPPER-001',
    12.00,
    1000,
    100
),
(
    'cc0e8400-e29b-41d4-a716-446655440003',
    '550e8400-e29b-41d4-a716-446655440001',
    'bb0e8400-e29b-41d4-a716-446655440003',
    '柒零纪念T恤',
    '柒零连锁品牌纪念T恤，100%纯棉',
    'QL-TSHIRT-001',
    88.00,
    200,
    20
),
(
    'cc0e8400-e29b-41d4-a716-446655440004',
    '550e8400-e29b-41d4-a716-446655440001',
    'bb0e8400-e29b-41d4-a716-446655440003',
    '柒零保温杯',
    '不锈钢保温杯，保温12小时',
    'QL-CUP-001',
    128.00,
    150,
    15
);
