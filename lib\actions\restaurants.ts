/**
 * 餐饮相关的 Server Actions
 * 处理餐厅和菜单的增删改查操作
 */

'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUser } from '@/lib/auth/actions'
import type { 
  RestaurantFormData, 
  MenuItemFormData,
  MenuCategoryFormData,
  RestaurantSearchParams,
  RestaurantActionResult,
  Restaurant,
  MenuItem,
  MenuCategory
} from '@/types/restaurant'

// 获取当前商家的餐厅列表
export async function getRestaurants(params?: RestaurantSearchParams): Promise<RestaurantActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const supabase = createClient()
    let query = supabase
      .from('restaurants')
      .select(`
        *,
        menu_categories:menu_categories(
          id,
          name,
          menu_items:menu_items(id)
        )
      `)
      .eq('merchant_id', user.merchant.id)

    // 应用搜索条件
    if (params?.query) {
      query = query.or(`name.ilike.%${params.query}%,description.ilike.%${params.query}%`)
    }

    if (params?.restaurant_type && params.restaurant_type.length > 0) {
      query = query.overlaps('restaurant_type', params.restaurant_type)
    }

    if (params?.min_price) {
      query = query.gte('average_price', params.min_price)
    }

    if (params?.max_price) {
      query = query.lte('average_price', params.max_price)
    }

    // 排序
    const sortBy = params?.sort_by || 'created_at'
    const sortOrder = params?.sort_order || 'desc'
    query = query.order(sortBy, { ascending: sortOrder === 'asc' })

    // 分页
    const page = params?.page || 1
    const limit = params?.limit || 20
    const offset = (page - 1) * limit
    query = query.range(offset, offset + limit - 1)

    const { data: restaurants, error } = await query

    if (error) {
      console.error('Get restaurants error:', error)
      return {
        success: false,
        error: '获取餐厅列表失败',
      }
    }

    return {
      success: true,
      data: restaurants as any,
    }
  } catch (error) {
    console.error('Get restaurants error:', error)
    return {
      success: false,
      error: '获取餐厅列表失败',
    }
  }
}

// 获取餐厅详情
export async function getRestaurantById(id: string): Promise<RestaurantActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const supabase = createClient()
    const { data: restaurant, error } = await supabase
      .from('restaurants')
      .select(`
        *,
        menu_categories:menu_categories(
          *,
          menu_items:menu_items(*)
        )
      `)
      .eq('id', id)
      .eq('merchant_id', user.merchant.id)
      .single()

    if (error) {
      console.error('Get restaurant by id error:', error)
      return {
        success: false,
        error: '获取餐厅详情失败',
      }
    }

    if (!restaurant) {
      return {
        success: false,
        error: '未找到餐厅信息',
      }
    }

    return {
      success: true,
      data: restaurant as any,
    }
  } catch (error) {
    console.error('Get restaurant by id error:', error)
    return {
      success: false,
      error: '获取餐厅详情失败',
    }
  }
}

// 创建餐厅
export async function createRestaurant(formData: RestaurantFormData): Promise<RestaurantActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    // 检查商家是否有餐饮业务类型
    if (!user.merchant.business_types?.includes('restaurant')) {
      return {
        success: false,
        error: '您的商家未开通餐饮业务',
      }
    }

    const restaurantData = {
      merchant_id: user.merchant.id,
      name: formData.name,
      description: formData.description || null,
      restaurant_type: formData.restaurant_type || [],
      address: formData.address || null,
      phone: formData.phone || null,
      email: formData.email || null,
      website: formData.website || null,
      images: formData.images || [],
      amenities: formData.amenities || [],
      business_hours: formData.business_hours || {},
      capacity: formData.capacity || null,
      average_price: formData.average_price || null,
      is_active: formData.is_active !== false,
    }

    const supabase = createClient()
    const { data: restaurant, error } = await supabase
      .from('restaurants')
      .insert(restaurantData)
      .select()
      .single()

    if (error) {
      console.error('Create restaurant error:', error)
      return {
        success: false,
        error: '创建餐厅失败',
      }
    }

    revalidatePath('/restaurants')

    return {
      success: true,
      data: restaurant,
      message: '餐厅创建成功',
    }
  } catch (error) {
    console.error('Create restaurant error:', error)
    return {
      success: false,
      error: '创建餐厅失败，请稍后重试',
    }
  }
}

// 更新餐厅信息
export async function updateRestaurant(id: string, formData: RestaurantFormData): Promise<RestaurantActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    // 验证餐厅所有权
    const supabase = createClient()
    const { data: existingRestaurant, error: fetchError } = await supabase
      .from('restaurants')
      .select('id, merchant_id')
      .eq('id', id)
      .eq('merchant_id', user.merchant.id)
      .single()

    if (fetchError || !existingRestaurant) {
      return {
        success: false,
        error: '无权修改该餐厅信息',
      }
    }

    const updateData = {
      name: formData.name,
      description: formData.description || null,
      restaurant_type: formData.restaurant_type || [],
      address: formData.address || null,
      phone: formData.phone || null,
      email: formData.email || null,
      website: formData.website || null,
      images: formData.images || [],
      amenities: formData.amenities || [],
      business_hours: formData.business_hours || {},
      capacity: formData.capacity || null,
      average_price: formData.average_price || null,
      is_active: formData.is_active !== false,
    }

    const { data: restaurant, error: updateError } = await supabase
      .from('restaurants')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (updateError) {
      console.error('Update restaurant error:', updateError)
      return {
        success: false,
        error: '更新餐厅信息失败',
      }
    }

    revalidatePath('/restaurants')
    revalidatePath(`/restaurants/${id}`)

    return {
      success: true,
      data: restaurant,
      message: '餐厅信息更新成功',
    }
  } catch (error) {
    console.error('Update restaurant error:', error)
    return {
      success: false,
      error: '更新餐厅信息失败，请稍后重试',
    }
  }
}

// 删除餐厅
export async function deleteRestaurant(id: string): Promise<RestaurantActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    // 验证餐厅所有权
    const supabase = createClient()
    const { data: existingRestaurant, error: fetchError } = await supabase
      .from('restaurants')
      .select('id, merchant_id')
      .eq('id', id)
      .eq('merchant_id', user.merchant.id)
      .single()

    if (fetchError || !existingRestaurant) {
      return {
        success: false,
        error: '无权删除该餐厅',
      }
    }

    const { error: deleteError } = await supabase
      .from('restaurants')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('Delete restaurant error:', deleteError)
      return {
        success: false,
        error: '删除餐厅失败',
      }
    }

    revalidatePath('/restaurants')

    return {
      success: true,
      message: '餐厅删除成功',
    }
  } catch (error) {
    console.error('Delete restaurant error:', error)
    return {
      success: false,
      error: '删除餐厅失败，请稍后重试',
    }
  }
}

// 获取菜单分类
export async function getMenuCategories(restaurantId: string): Promise<RestaurantActionResult> {
  try {
    const user = await getCurrentUser()

    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const supabase = createClient()
    const { data: categories, error } = await supabase
      .from('menu_categories')
      .select(`
        *,
        menu_items:menu_items(id, name, price, is_available)
      `)
      .eq('restaurant_id', restaurantId)
      .order('sort_order', { ascending: true })

    if (error) {
      console.error('Get menu categories error:', error)
      return {
        success: false,
        error: '获取菜单分类失败',
      }
    }

    return {
      success: true,
      data: categories as any,
    }
  } catch (error) {
    console.error('Get menu categories error:', error)
    return {
      success: false,
      error: '获取菜单分类失败',
    }
  }
}

// 创建菜单分类
export async function createMenuCategory(formData: MenuCategoryFormData): Promise<RestaurantActionResult> {
  try {
    const user = await getCurrentUser()

    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    // 验证餐厅所有权
    const supabase = createClient()
    const { data: restaurant, error: restaurantError } = await supabase
      .from('restaurants')
      .select('id, merchant_id')
      .eq('id', formData.restaurant_id)
      .eq('merchant_id', user.merchant.id)
      .single()

    if (restaurantError || !restaurant) {
      return {
        success: false,
        error: '无权操作该餐厅',
      }
    }

    const categoryData = {
      restaurant_id: formData.restaurant_id,
      name: formData.name,
      description: formData.description || null,
      image: formData.image || null,
      sort_order: formData.sort_order || 0,
      is_active: formData.is_active !== false,
    }

    const { data: category, error } = await supabase
      .from('menu_categories')
      .insert(categoryData)
      .select()
      .single()

    if (error) {
      console.error('Create menu category error:', error)
      return {
        success: false,
        error: '创建菜单分类失败',
      }
    }

    revalidatePath('/restaurants')
    revalidatePath(`/restaurants/${formData.restaurant_id}`)

    return {
      success: true,
      data: category,
      message: '菜单分类创建成功',
    }
  } catch (error) {
    console.error('Create menu category error:', error)
    return {
      success: false,
      error: '创建菜单分类失败，请稍后重试',
    }
  }
}

// 创建菜品
export async function createMenuItem(formData: MenuItemFormData): Promise<RestaurantActionResult> {
  try {
    const user = await getCurrentUser()

    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    // 验证餐厅所有权
    const supabase = createClient()
    const { data: restaurant, error: restaurantError } = await supabase
      .from('restaurants')
      .select('id, merchant_id')
      .eq('id', formData.restaurant_id)
      .eq('merchant_id', user.merchant.id)
      .single()

    if (restaurantError || !restaurant) {
      return {
        success: false,
        error: '无权操作该餐厅',
      }
    }

    const menuItemData = {
      restaurant_id: formData.restaurant_id,
      category_id: formData.category_id || null,
      name: formData.name,
      description: formData.description || null,
      price: formData.price,
      original_price: formData.original_price || null,
      images: formData.images || [],
      ingredients: formData.ingredients || [],
      allergens: formData.allergens || [],
      nutrition_info: formData.nutrition_info || {},
      preparation_time: formData.preparation_time || null,
      is_spicy: formData.is_spicy || false,
      is_vegetarian: formData.is_vegetarian || false,
      is_vegan: formData.is_vegan || false,
      is_gluten_free: formData.is_gluten_free || false,
      is_available: formData.is_available !== false,
      sort_order: formData.sort_order || 0,
    }

    const { data: menuItem, error } = await supabase
      .from('menu_items')
      .insert(menuItemData)
      .select()
      .single()

    if (error) {
      console.error('Create menu item error:', error)
      return {
        success: false,
        error: '创建菜品失败',
      }
    }

    revalidatePath('/restaurants')
    revalidatePath(`/restaurants/${formData.restaurant_id}`)

    return {
      success: true,
      data: menuItem,
      message: '菜品创建成功',
    }
  } catch (error) {
    console.error('Create menu item error:', error)
    return {
      success: false,
      error: '创建菜品失败，请稍后重试',
    }
  }
}
