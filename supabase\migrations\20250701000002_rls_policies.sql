-- 柒零支付连锁平台 Row Level Security (RLS) 策略
-- 创建时间: 2025-07-01
-- 描述: 配置数据安全访问策略，确保用户只能访问自己的数据

-- 启用所有表的 RLS
ALTER TABLE merchants ENABLE ROW LEVEL SECURITY;
ALTER TABLE hotels ENABLE ROW LEVEL SECURITY;
ALTER TABLE room_types ENABLE ROW LEVEL SECURITY;
ALTER TABLE restaurants ENABLE ROW LEVEL SECURITY;
ALTER TABLE menu_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE menu_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- 商家表 RLS 策略
-- 用户只能访问自己的商家信息
CREATE POLICY "Users can view their own merchant data" ON merchants
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own merchant data" ON merchants
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own merchant data" ON merchants
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own merchant data" ON merchants
    FOR DELETE USING (auth.uid() = user_id);

-- 酒店表 RLS 策略
-- 用户只能访问自己商家的酒店
CREATE POLICY "Users can view their own hotels" ON hotels
    FOR SELECT USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert hotels for their merchants" ON hotels
    FOR INSERT WITH CHECK (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own hotels" ON hotels
    FOR UPDATE USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own hotels" ON hotels
    FOR DELETE USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

-- 房间类型表 RLS 策略
CREATE POLICY "Users can view their own room types" ON room_types
    FOR SELECT USING (
        hotel_id IN (
            SELECT h.id FROM hotels h
            JOIN merchants m ON h.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert room types for their hotels" ON room_types
    FOR INSERT WITH CHECK (
        hotel_id IN (
            SELECT h.id FROM hotels h
            JOIN merchants m ON h.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own room types" ON room_types
    FOR UPDATE USING (
        hotel_id IN (
            SELECT h.id FROM hotels h
            JOIN merchants m ON h.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own room types" ON room_types
    FOR DELETE USING (
        hotel_id IN (
            SELECT h.id FROM hotels h
            JOIN merchants m ON h.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

-- 餐厅表 RLS 策略
CREATE POLICY "Users can view their own restaurants" ON restaurants
    FOR SELECT USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert restaurants for their merchants" ON restaurants
    FOR INSERT WITH CHECK (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own restaurants" ON restaurants
    FOR UPDATE USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own restaurants" ON restaurants
    FOR DELETE USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

-- 菜单分类表 RLS 策略
CREATE POLICY "Users can view their own menu categories" ON menu_categories
    FOR SELECT USING (
        restaurant_id IN (
            SELECT r.id FROM restaurants r
            JOIN merchants m ON r.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert menu categories for their restaurants" ON menu_categories
    FOR INSERT WITH CHECK (
        restaurant_id IN (
            SELECT r.id FROM restaurants r
            JOIN merchants m ON r.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own menu categories" ON menu_categories
    FOR UPDATE USING (
        restaurant_id IN (
            SELECT r.id FROM restaurants r
            JOIN merchants m ON r.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own menu categories" ON menu_categories
    FOR DELETE USING (
        restaurant_id IN (
            SELECT r.id FROM restaurants r
            JOIN merchants m ON r.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

-- 菜品表 RLS 策略
CREATE POLICY "Users can view their own menu items" ON menu_items
    FOR SELECT USING (
        restaurant_id IN (
            SELECT r.id FROM restaurants r
            JOIN merchants m ON r.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert menu items for their restaurants" ON menu_items
    FOR INSERT WITH CHECK (
        restaurant_id IN (
            SELECT r.id FROM restaurants r
            JOIN merchants m ON r.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own menu items" ON menu_items
    FOR UPDATE USING (
        restaurant_id IN (
            SELECT r.id FROM restaurants r
            JOIN merchants m ON r.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own menu items" ON menu_items
    FOR DELETE USING (
        restaurant_id IN (
            SELECT r.id FROM restaurants r
            JOIN merchants m ON r.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

-- 商品分类表 RLS 策略
CREATE POLICY "Users can view their own product categories" ON product_categories
    FOR SELECT USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert product categories for their merchants" ON product_categories
    FOR INSERT WITH CHECK (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own product categories" ON product_categories
    FOR UPDATE USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own product categories" ON product_categories
    FOR DELETE USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

-- 商品表 RLS 策略
CREATE POLICY "Users can view their own products" ON products
    FOR SELECT USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert products for their merchants" ON products
    FOR INSERT WITH CHECK (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own products" ON products
    FOR UPDATE USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own products" ON products
    FOR DELETE USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

-- 订单表 RLS 策略
CREATE POLICY "Users can view their own orders" ON orders
    FOR SELECT USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert orders for their merchants" ON orders
    FOR INSERT WITH CHECK (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own orders" ON orders
    FOR UPDATE USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own orders" ON orders
    FOR DELETE USING (
        merchant_id IN (
            SELECT id FROM merchants WHERE user_id = auth.uid()
        )
    );

-- 支付记录表 RLS 策略
CREATE POLICY "Users can view their own payments" ON payments
    FOR SELECT USING (
        order_id IN (
            SELECT o.id FROM orders o
            JOIN merchants m ON o.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert payments for their orders" ON payments
    FOR INSERT WITH CHECK (
        order_id IN (
            SELECT o.id FROM orders o
            JOIN merchants m ON o.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update their own payments" ON payments
    FOR UPDATE USING (
        order_id IN (
            SELECT o.id FROM orders o
            JOIN merchants m ON o.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete their own payments" ON payments
    FOR DELETE USING (
        order_id IN (
            SELECT o.id FROM orders o
            JOIN merchants m ON o.merchant_id = m.id
            WHERE m.user_id = auth.uid()
        )
    );
