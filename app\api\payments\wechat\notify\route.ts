/**
 * 微信支付回调API
 * 处理微信支付的异步通知
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { paymentGateway } from '@/lib/payments/payment-gateway'

// POST 处理微信支付异步通知
export async function POST(request: NextRequest) {
  try {
    const xmlData = await request.text()
    
    // 解析XML数据
    const data = parseXmlToObject(xmlData)

    console.log('Wechat pay callback received:', data)

    // 验证回调数据
    const verifyResult = await paymentGateway.verifyCallback('wechat_pay', data)

    if (!verifyResult.success) {
      console.error('Wechat pay callback verification failed:', verifyResult.error)
      return new Response(buildXmlResponse('FAIL', 'Signature verification failed'), {
        headers: { 'Content-Type': 'application/xml' }
      })
    }

    const { paymentId, status, amount } = verifyResult

    if (!paymentId || !status) {
      console.error('Wechat pay callback data incomplete:', { paymentId, status })
      return new Response(buildXmlResponse('FAIL', 'Invalid data'), {
        headers: { 'Content-Type': 'application/xml' }
      })
    }

    // 更新支付状态
    const supabase = createClient()
    
    // 查找支付记录
    const { data: payment, error: findError } = await supabase
      .from('payments')
      .select('id, status, order_id, merchant_id')
      .eq('payment_id', paymentId)
      .single()

    if (findError || !payment) {
      console.error('Payment not found:', paymentId, findError)
      return new Response(buildXmlResponse('FAIL', 'Payment not found'), {
        headers: { 'Content-Type': 'application/xml' }
      })
    }

    // 如果状态已经是最终状态，不需要更新
    if (['completed', 'failed', 'cancelled', 'refunded'].includes(payment.status)) {
      console.log('Payment status is already final:', payment.status)
      return new Response(buildXmlResponse('SUCCESS', 'OK'), {
        headers: { 'Content-Type': 'application/xml' }
      })
    }

    // 更新支付状态
    const updateData: any = {
      status,
      updated_at: new Date().toISOString(),
    }

    if (status === 'completed') {
      updateData.paid_amount = amount
      updateData.paid_at = new Date().toISOString()
    }

    const { error: updateError } = await supabase
      .from('payments')
      .update(updateData)
      .eq('id', payment.id)

    if (updateError) {
      console.error('Update payment status error:', updateError)
      return new Response(buildXmlResponse('FAIL', 'Update failed'), {
        headers: { 'Content-Type': 'application/xml' }
      })
    }

    // 如果支付成功，更新订单状态
    if (status === 'completed') {
      const { error: orderUpdateError } = await supabase
        .from('orders')
        .update({
          payment_status: 'paid',
          status: 'processing', // 支付成功后订单进入处理状态
          updated_at: new Date().toISOString(),
        })
        .eq('id', payment.order_id)

      if (orderUpdateError) {
        console.error('Update order status error:', orderUpdateError)
        // 不返回错误，因为支付状态已经更新成功
      }
    }

    // 记录回调日志
    await supabase
      .from('payment_logs')
      .insert({
        payment_id: payment.id,
        action: 'wechat_callback',
        status,
        data: data,
        created_at: new Date().toISOString(),
      })

    console.log(`Wechat pay callback processed: ${paymentId} -> ${status}`)

    return new Response(buildXmlResponse('SUCCESS', 'OK'), {
      headers: { 'Content-Type': 'application/xml' }
    })

  } catch (error) {
    console.error('Wechat pay callback error:', error)
    return new Response(buildXmlResponse('FAIL', 'Internal error'), {
      headers: { 'Content-Type': 'application/xml' }
    })
  }
}

// 解析XML为对象
function parseXmlToObject(xml: string): Record<string, string> {
  const result: Record<string, string> = {}
  
  // 简单的XML解析（实际项目中应该使用专业的XML解析库）
  const regex = /<(\w+)><!\[CDATA\[(.*?)\]\]><\/\1>|<(\w+)>(.*?)<\/\3>/g
  let match

  while ((match = regex.exec(xml)) !== null) {
    const key = match[1] || match[3]
    const value = match[2] || match[4]
    if (key && value !== undefined) {
      result[key] = value
    }
  }

  return result
}

// 构建XML响应
function buildXmlResponse(returnCode: string, returnMsg: string): string {
  return `<xml>
    <return_code><![CDATA[${returnCode}]]></return_code>
    <return_msg><![CDATA[${returnMsg}]]></return_msg>
  </xml>`
}

// OPTIONS 处理预检请求
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
