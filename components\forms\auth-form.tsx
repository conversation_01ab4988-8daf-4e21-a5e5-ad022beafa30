/**
 * 认证表单组件
 * 可复用的登录和注册表单
 */

'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { cn } from '@/lib/utils'
import type { LoginFormData, RegisterFormData } from '@/lib/validations/auth'
import { loginSchema, registerSchema } from '@/lib/validations/auth'

interface AuthFormProps {
  type: 'login' | 'register'
  onSubmit: (data: LoginFormData | RegisterFormData) => Promise<void>
  isLoading?: boolean
  className?: string
}

export function AuthForm({ type, onSubmit, isLoading = false, className }: AuthFormProps) {
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const schema = type === 'login' ? loginSchema : registerSchema
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<LoginFormData | RegisterFormData>({
    resolver: zodResolver(schema),
    defaultValues: type === 'register' ? {
      businessTypes: [],
      agreeToTerms: false,
    } : undefined,
  })

  const businessTypes = type === 'register' ? watch('businessTypes' as keyof RegisterFormData) : []

  const handleBusinessTypeChange = (value: string, checked: boolean) => {
    if (type !== 'register') return

    const currentTypes = businessTypes as string[] || []
    const newTypes = checked
      ? [...currentTypes, value]
      : currentTypes.filter(t => t !== value)

    setValue('businessTypes' as keyof RegisterFormData, newTypes as any)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className={cn('space-y-6', className)}>
      {/* 邮箱 */}
      <div>
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
          邮箱地址 *
        </label>
        <input
          {...register('email')}
          type="email"
          id="email"
          autoComplete="email"
          className={cn(
            'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400',
            'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            errors.email && 'border-red-300 focus:ring-red-500 focus:border-red-500'
          )}
          placeholder="请输入邮箱地址"
          disabled={isLoading}
        />
        {errors.email && (
          <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
        )}
      </div>

      {/* 注册时的姓名 */}
      {type === 'register' && (
        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
            姓名 *
          </label>
          <input
            {...register('name' as keyof RegisterFormData)}
            type="text"
            id="name"
            autoComplete="name"
            className={cn(
              'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400',
              'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
              errors.name && 'border-red-300 focus:ring-red-500 focus:border-red-500'
            )}
            placeholder="请输入您的姓名"
            disabled={isLoading}
          />
          {errors.name && (
            <p className="mt-1 text-sm text-red-600">{errors.name.message}</p>
          )}
        </div>
      )}

      {/* 注册时的商家名称 */}
      {type === 'register' && (
        <div>
          <label htmlFor="merchantName" className="block text-sm font-medium text-gray-700 mb-2">
            商家名称 *
          </label>
          <input
            {...register('merchantName' as keyof RegisterFormData)}
            type="text"
            id="merchantName"
            className={cn(
              'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400',
              'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
              errors.merchantName && 'border-red-300 focus:ring-red-500 focus:border-red-500'
            )}
            placeholder="请输入商家名称"
            disabled={isLoading}
          />
          {errors.merchantName && (
            <p className="mt-1 text-sm text-red-600">{errors.merchantName.message}</p>
          )}
        </div>
      )}

      {/* 注册时的业务类型 */}
      {type === 'register' && (
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            业务类型 *
          </label>
          <div className="space-y-2">
            {[
              { value: 'hotel', label: '酒店管理' },
              { value: 'restaurant', label: '餐饮管理' },
              { value: 'product', label: '商品管理' },
            ].map((option) => (
              <label key={option.value} className="flex items-center">
                <input
                  type="checkbox"
                  value={option.value}
                  checked={(businessTypes as string[] || []).includes(option.value)}
                  onChange={(e) => handleBusinessTypeChange(option.value, e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={isLoading}
                />
                <span className="ml-2 text-sm text-gray-700">{option.label}</span>
              </label>
            ))}
          </div>
          {errors.businessTypes && (
            <p className="mt-1 text-sm text-red-600">{errors.businessTypes.message}</p>
          )}
        </div>
      )}

      {/* 注册时的手机号 */}
      {type === 'register' && (
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
            手机号码
          </label>
          <input
            {...register('phone' as keyof RegisterFormData)}
            type="tel"
            id="phone"
            autoComplete="tel"
            className={cn(
              'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400',
              'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
              errors.phone && 'border-red-300 focus:ring-red-500 focus:border-red-500'
            )}
            placeholder="请输入手机号码（可选）"
            disabled={isLoading}
          />
          {errors.phone && (
            <p className="mt-1 text-sm text-red-600">{errors.phone.message}</p>
          )}
        </div>
      )}

      {/* 密码 */}
      <div>
        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
          密码 *
        </label>
        <div className="relative">
          <input
            {...register('password')}
            type={showPassword ? 'text' : 'password'}
            id="password"
            autoComplete={type === 'login' ? 'current-password' : 'new-password'}
            className={cn(
              'w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm placeholder-gray-400',
              'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
              errors.password && 'border-red-300 focus:ring-red-500 focus:border-red-500'
            )}
            placeholder={type === 'login' ? '请输入密码' : '请输入密码（至少6位，包含大小写字母和数字）'}
            disabled={isLoading}
          />
          <button
            type="button"
            className="absolute inset-y-0 right-0 pr-3 flex items-center"
            onClick={() => setShowPassword(!showPassword)}
            disabled={isLoading}
          >
            <span className="text-gray-400 text-sm">
              {showPassword ? '隐藏' : '显示'}
            </span>
          </button>
        </div>
        {errors.password && (
          <p className="mt-1 text-sm text-red-600">{errors.password.message}</p>
        )}
      </div>

      {/* 注册时的确认密码 */}
      {type === 'register' && (
        <div>
          <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-2">
            确认密码 *
          </label>
          <div className="relative">
            <input
              {...register('confirmPassword' as keyof RegisterFormData)}
              type={showConfirmPassword ? 'text' : 'password'}
              id="confirmPassword"
              autoComplete="new-password"
              className={cn(
                'w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm placeholder-gray-400',
                'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
                errors.confirmPassword && 'border-red-300 focus:ring-red-500 focus:border-red-500'
              )}
              placeholder="请再次输入密码"
              disabled={isLoading}
            />
            <button
              type="button"
              className="absolute inset-y-0 right-0 pr-3 flex items-center"
              onClick={() => setShowConfirmPassword(!showConfirmPassword)}
              disabled={isLoading}
            >
              <span className="text-gray-400 text-sm">
                {showConfirmPassword ? '隐藏' : '显示'}
              </span>
            </button>
          </div>
          {errors.confirmPassword && (
            <p className="mt-1 text-sm text-red-600">{errors.confirmPassword.message}</p>
          )}
        </div>
      )}

      {/* 注册时的服务条款 */}
      {type === 'register' && (
        <div>
          <label className="flex items-start">
            <input
              {...register('agreeToTerms' as keyof RegisterFormData)}
              type="checkbox"
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mt-0.5"
              disabled={isLoading}
            />
            <span className="ml-2 text-sm text-gray-700">
              我已阅读并同意{' '}
              <a href="/terms" className="text-blue-600 hover:text-blue-500">
                服务条款
              </a>{' '}
              和{' '}
              <a href="/privacy" className="text-blue-600 hover:text-blue-500">
                隐私政策
              </a>
            </span>
          </label>
          {errors.agreeToTerms && (
            <p className="mt-1 text-sm text-red-600">{errors.agreeToTerms.message}</p>
          )}
        </div>
      )}

      {/* 提交按钮 */}
      <button
        type="submit"
        disabled={isLoading}
        className={cn(
          'w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white',
          'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500',
          'disabled:opacity-50 disabled:cursor-not-allowed',
          isLoading && 'bg-blue-400'
        )}
      >
        {isLoading ? (
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
            {type === 'login' ? '登录中...' : '注册中...'}
          </div>
        ) : (
          type === 'login' ? '登录' : '注册'
        )}
      </button>
    </form>
  )
}
