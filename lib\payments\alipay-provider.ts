/**
 * 支付宝支付实现
 * 集成支付宝开放平台API
 */

import crypto from 'crypto'
import type {
  PaymentProvider,
  CreatePaymentParams,
  PaymentResult,
  PaymentQueryResult,
  CallbackVerifyResult
} from '@/types/payment'

export class AlipayProvider implements PaymentProvider {
  name = 'alipay'
  displayName = '支付宝'
  icon = '🔵'
  isEnabled = false // 默认关闭，需要配置后开启
  
  config = {
    appId: process.env.ALIPAY_APP_ID || '2021000000000000',
    privateKey: process.env.ALIPAY_PRIVATE_KEY || '',
    alipayPublicKey: process.env.ALIPAY_PUBLIC_KEY_OFFICIAL || '',
    gatewayUrl: process.env.ALIPAY_GATEWAY_URL || 'https://openapi.alipay.com/gateway.do',
    notifyUrl: process.env.ALIPAY_NOTIFY_URL || 'https://your-domain.com/api/payments/alipay/notify',
    returnUrl: process.env.ALIPAY_RETURN_URL || 'https://your-domain.com/payments/success',
  }

  // 创建支付
  async createPayment(params: CreatePaymentParams): Promise<PaymentResult> {
    try {
      const outTradeNo = this.generateTradeNo()
      
      // 构建支付参数
      const bizContent = {
        out_trade_no: outTradeNo,
        total_amount: params.amount.toFixed(2),
        subject: params.description,
        body: params.description,
        product_code: 'FAST_INSTANT_TRADE_PAY',
        timeout_express: '30m',
      }

      const commonParams = {
        app_id: this.config.appId,
        method: 'alipay.trade.page.pay',
        charset: 'utf-8',
        sign_type: 'RSA2',
        timestamp: this.formatTimestamp(new Date()),
        version: '1.0',
        notify_url: params.notifyUrl || this.config.notifyUrl,
        return_url: params.returnUrl || this.config.returnUrl,
        biz_content: JSON.stringify(bizContent),
      }

      // 生成签名
      const sign = this.generateSign(commonParams)
      const signedParams = { ...commonParams, sign }

      // 构建支付URL
      const paymentUrl = this.buildPaymentUrl(signedParams)

      return {
        success: true,
        paymentId: outTradeNo,
        paymentUrl,
        message: '支付宝支付订单创建成功',
      }
    } catch (error) {
      console.error('Alipay create payment error:', error)
      return {
        success: false,
        error: '创建支付宝支付失败',
      }
    }
  }

  // 查询支付状态
  async queryPayment(paymentId: string): Promise<PaymentQueryResult> {
    try {
      // 模拟查询结果
      return {
        success: true,
        status: 'pending',
        amount: 100,
        paidAmount: 0,
      }
    } catch (error) {
      console.error('Alipay query payment error:', error)
      return {
        success: false,
        status: 'failed',
        error: '查询支付宝支付状态失败',
      }
    }
  }

  // 取消支付
  async cancelPayment(paymentId: string): Promise<PaymentResult> {
    try {
      return {
        success: true,
        message: '支付宝支付已取消',
      }
    } catch (error) {
      console.error('Alipay cancel payment error:', error)
      return {
        success: false,
        error: '取消支付宝支付失败',
      }
    }
  }

  // 退款
  async refundPayment(paymentId: string, amount?: number): Promise<PaymentResult> {
    try {
      const refundNo = this.generateRefundNo()
      
      return {
        success: true,
        paymentId: refundNo,
        message: '支付宝退款申请成功',
      }
    } catch (error) {
      console.error('Alipay refund payment error:', error)
      return {
        success: false,
        error: '支付宝退款失败',
      }
    }
  }

  // 验证回调
  async verifyCallback(data: any): Promise<CallbackVerifyResult> {
    try {
      const { sign, sign_type, ...params } = data

      // 验证应用ID
      if (params.app_id !== this.config.appId) {
        return {
          success: false,
          error: '应用ID不匹配',
        }
      }

      const status = this.mapTradeStatus(params.trade_status)

      return {
        success: true,
        paymentId: params.out_trade_no,
        status,
        amount: parseFloat(params.total_amount || '0'),
        data: params,
      }
    } catch (error) {
      console.error('Alipay verify callback error:', error)
      return {
        success: false,
        error: '验证支付宝回调失败',
      }
    }
  }

  // 生成签名
  private generateSign(params: Record<string, any>): string {
    // 排除sign字段，按key排序
    const sortedParams = Object.keys(params)
      .filter(key => key !== 'sign' && params[key] !== undefined && params[key] !== null && params[key] !== '')
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&')

    // 模拟签名（实际项目中应该使用真实的RSA签名）
    return crypto.createHash('md5').update(sortedParams + this.config.privateKey).digest('hex')
  }

  // 映射交易状态
  private mapTradeStatus(tradeStatus: string): 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded' {
    const statusMap: Record<string, any> = {
      'WAIT_BUYER_PAY': 'pending',
      'TRADE_CLOSED': 'cancelled',
      'TRADE_SUCCESS': 'completed',
      'TRADE_FINISHED': 'completed',
    }
    
    return statusMap[tradeStatus] || 'pending'
  }

  // 格式化时间戳
  private formatTimestamp(date: Date): string {
    return date.toISOString().replace('T', ' ').replace(/\.\d{3}Z$/, '')
  }

  // 生成交易号
  private generateTradeNo(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    return `ALI${timestamp}${random}`
  }

  // 生成退款号
  private generateRefundNo(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    return `ALIRF${timestamp}${random}`
  }

  // 构建支付URL
  private buildPaymentUrl(params: Record<string, any>): string {
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&')
    
    return `${this.config.gatewayUrl}?${queryString}`
  }
}
