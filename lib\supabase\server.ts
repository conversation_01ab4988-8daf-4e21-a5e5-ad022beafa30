/**
 * Supabase 服务端客户端配置
 * 用于服务端组件、API路由和中间件
 */

import { createServerClient } from '@supabase/ssr'
import { cookies } from 'next/headers'
import type { Database } from '@/types/database'

// 创建服务端 Supabase 客户端
export const createClient = () => {
  const cookieStore = cookies()

  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    {
      cookies: {
        getAll() {
          return cookieStore.getAll()
        },
        setAll(cookiesToSet) {
          try {
            cookiesToSet.forEach(({ name, value, options }) => {
              cookieStore.set(name, value, options)
            })
          } catch (error) {
            // 在中间件中可能会出现错误，这是正常的
            console.warn('Failed to set cookies in middleware:', error)
          }
        },
      },
    }
  )
}

// 创建管理员客户端（使用 service role key）
export const createAdminClient = () => {
  return createServerClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      cookies: {
        getAll() {
          return []
        },
        setAll() {
          // 管理员客户端不需要设置 cookies
        },
      },
    }
  )
}

// 类型导出
export type SupabaseServerClient = ReturnType<typeof createClient>
export type SupabaseAdminClient = ReturnType<typeof createAdminClient>
