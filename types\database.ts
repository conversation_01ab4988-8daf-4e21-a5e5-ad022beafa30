/**
 * 数据库类型定义
 * 基于 Supabase 数据库架构自动生成
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

// 枚举类型定义
export type BusinessType = 'hotel' | 'restaurant' | 'product'
export type OrderStatus = 'pending' | 'confirmed' | 'processing' | 'completed' | 'cancelled'
export type PaymentStatus = 'pending' | 'processing' | 'success' | 'failed' | 'refunded'
export type PaymentMethod = 'qiling' | 'alipay' | 'wechat' | 'cash'
export type UserRole = 'admin' | 'merchant' | 'staff'
export type MerchantStatus = 'active' | 'inactive' | 'suspended'

export interface Database {
  public: {
    Tables: {
      merchants: {
        Row: {
          id: string
          user_id: string | null
          name: string
          description: string | null
          logo: string | null
          business_types: BusinessType[]
          contact_info: Json
          business_hours: Json
          status: MerchantStatus
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id?: string | null
          name: string
          description?: string | null
          logo?: string | null
          business_types?: BusinessType[]
          contact_info?: Json
          business_hours?: Json
          status?: MerchantStatus
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string | null
          name?: string
          description?: string | null
          logo?: string | null
          business_types?: BusinessType[]
          contact_info?: Json
          business_hours?: Json
          status?: MerchantStatus
          created_at?: string
          updated_at?: string
        }
      }
      hotels: {
        Row: {
          id: string
          merchant_id: string | null
          name: string
          description: string | null
          address: string | null
          phone: string | null
          email: string | null
          images: string[]
          amenities: string[]
          check_in_time: string | null
          check_out_time: string | null
          rating: number | null
          total_rooms: number | null
          available_rooms: number | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          merchant_id?: string | null
          name: string
          description?: string | null
          address?: string | null
          phone?: string | null
          email?: string | null
          images?: string[]
          amenities?: string[]
          check_in_time?: string | null
          check_out_time?: string | null
          rating?: number | null
          total_rooms?: number | null
          available_rooms?: number | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          merchant_id?: string | null
          name?: string
          description?: string | null
          address?: string | null
          phone?: string | null
          email?: string | null
          images?: string[]
          amenities?: string[]
          check_in_time?: string | null
          check_out_time?: string | null
          rating?: number | null
          total_rooms?: number | null
          available_rooms?: number | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      room_types: {
        Row: {
          id: string
          hotel_id: string | null
          name: string
          description: string | null
          images: string[]
          base_price: number
          max_occupancy: number | null
          size_sqm: number | null
          amenities: string[]
          total_rooms: number | null
          available_rooms: number | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          hotel_id?: string | null
          name: string
          description?: string | null
          images?: string[]
          base_price: number
          max_occupancy?: number | null
          size_sqm?: number | null
          amenities?: string[]
          total_rooms?: number | null
          available_rooms?: number | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          hotel_id?: string | null
          name?: string
          description?: string | null
          images?: string[]
          base_price?: number
          max_occupancy?: number | null
          size_sqm?: number | null
          amenities?: string[]
          total_rooms?: number | null
          available_rooms?: number | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      restaurants: {
        Row: {
          id: string
          merchant_id: string | null
          name: string
          description: string | null
          address: string | null
          phone: string | null
          email: string | null
          images: string[]
          cuisine_types: string[]
          price_range: string | null
          rating: number | null
          capacity: number | null
          delivery_available: boolean | null
          takeout_available: boolean | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          merchant_id?: string | null
          name: string
          description?: string | null
          address?: string | null
          phone?: string | null
          email?: string | null
          images?: string[]
          cuisine_types?: string[]
          price_range?: string | null
          rating?: number | null
          capacity?: number | null
          delivery_available?: boolean | null
          takeout_available?: boolean | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          merchant_id?: string | null
          name?: string
          description?: string | null
          address?: string | null
          phone?: string | null
          email?: string | null
          images?: string[]
          cuisine_types?: string[]
          price_range?: string | null
          rating?: number | null
          capacity?: number | null
          delivery_available?: boolean | null
          takeout_available?: boolean | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      menu_categories: {
        Row: {
          id: string
          restaurant_id: string | null
          name: string
          description: string | null
          sort_order: number | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          restaurant_id?: string | null
          name: string
          description?: string | null
          sort_order?: number | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          restaurant_id?: string | null
          name?: string
          description?: string | null
          sort_order?: number | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      menu_items: {
        Row: {
          id: string
          restaurant_id: string | null
          category_id: string | null
          name: string
          description: string | null
          images: string[]
          price: number
          original_price: number | null
          ingredients: string[]
          allergens: string[]
          calories: number | null
          preparation_time: number | null
          is_vegetarian: boolean | null
          is_vegan: boolean | null
          is_spicy: boolean | null
          is_available: boolean | null
          sort_order: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          restaurant_id?: string | null
          category_id?: string | null
          name: string
          description?: string | null
          images?: string[]
          price: number
          original_price?: number | null
          ingredients?: string[]
          allergens?: string[]
          calories?: number | null
          preparation_time?: number | null
          is_vegetarian?: boolean | null
          is_vegan?: boolean | null
          is_spicy?: boolean | null
          is_available?: boolean | null
          sort_order?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          restaurant_id?: string | null
          category_id?: string | null
          name?: string
          description?: string | null
          images?: string[]
          price?: number
          original_price?: number | null
          ingredients?: string[]
          allergens?: string[]
          calories?: number | null
          preparation_time?: number | null
          is_vegetarian?: boolean | null
          is_vegan?: boolean | null
          is_spicy?: boolean | null
          is_available?: boolean | null
          sort_order?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      product_categories: {
        Row: {
          id: string
          merchant_id: string | null
          name: string
          description: string | null
          image: string | null
          parent_id: string | null
          sort_order: number | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          merchant_id?: string | null
          name: string
          description?: string | null
          image?: string | null
          parent_id?: string | null
          sort_order?: number | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          merchant_id?: string | null
          name?: string
          description?: string | null
          image?: string | null
          parent_id?: string | null
          sort_order?: number | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      products: {
        Row: {
          id: string
          merchant_id: string | null
          category_id: string | null
          name: string
          description: string | null
          images: string[]
          sku: string | null
          barcode: string | null
          price: number
          original_price: number | null
          cost_price: number | null
          stock_quantity: number | null
          min_stock_level: number | null
          weight: number | null
          dimensions: Json | null
          tags: string[]
          is_digital: boolean | null
          is_active: boolean | null
          sort_order: number | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          merchant_id?: string | null
          category_id?: string | null
          name: string
          description?: string | null
          images?: string[]
          sku?: string | null
          barcode?: string | null
          price: number
          original_price?: number | null
          cost_price?: number | null
          stock_quantity?: number | null
          min_stock_level?: number | null
          weight?: number | null
          dimensions?: Json | null
          tags?: string[]
          is_digital?: boolean | null
          is_active?: boolean | null
          sort_order?: number | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          merchant_id?: string | null
          category_id?: string | null
          name?: string
          description?: string | null
          images?: string[]
          sku?: string | null
          barcode?: string | null
          price?: number
          original_price?: number | null
          cost_price?: number | null
          stock_quantity?: number | null
          min_stock_level?: number | null
          weight?: number | null
          dimensions?: Json | null
          tags?: string[]
          is_digital?: boolean | null
          is_active?: boolean | null
          sort_order?: number | null
          created_at?: string
          updated_at?: string
        }
      }
      orders: {
        Row: {
          id: string
          merchant_id: string | null
          business_type: BusinessType
          business_item_id: string
          order_number: string
          customer_info: Json
          items: Json
          subtotal: number
          tax_amount: number | null
          discount_amount: number | null
          total_amount: number
          currency: string | null
          status: OrderStatus | null
          notes: string | null
          special_requests: string | null
          delivery_info: Json | null
          check_in_date: string | null
          check_out_date: string | null
          reservation_time: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          merchant_id?: string | null
          business_type: BusinessType
          business_item_id: string
          order_number?: string
          customer_info: Json
          items: Json
          subtotal: number
          tax_amount?: number | null
          discount_amount?: number | null
          total_amount?: number
          currency?: string | null
          status?: OrderStatus | null
          notes?: string | null
          special_requests?: string | null
          delivery_info?: Json | null
          check_in_date?: string | null
          check_out_date?: string | null
          reservation_time?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          merchant_id?: string | null
          business_type?: BusinessType
          business_item_id?: string
          order_number?: string
          customer_info?: Json
          items?: Json
          subtotal?: number
          tax_amount?: number | null
          discount_amount?: number | null
          total_amount?: number
          currency?: string | null
          status?: OrderStatus | null
          notes?: string | null
          special_requests?: string | null
          delivery_info?: Json | null
          check_in_date?: string | null
          check_out_date?: string | null
          reservation_time?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          order_id: string | null
          payment_method: PaymentMethod
          amount: number
          currency: string | null
          status: PaymentStatus | null
          transaction_id: string | null
          external_payment_id: string | null
          payment_data: Json | null
          paid_at: string | null
          refunded_at: string | null
          refund_amount: number | null
          failure_reason: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          order_id?: string | null
          payment_method: PaymentMethod
          amount: number
          currency?: string | null
          status?: PaymentStatus | null
          transaction_id?: string | null
          external_payment_id?: string | null
          payment_data?: Json | null
          paid_at?: string | null
          refunded_at?: string | null
          refund_amount?: number | null
          failure_reason?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          order_id?: string | null
          payment_method?: PaymentMethod
          amount?: number
          currency?: string | null
          status?: PaymentStatus | null
          transaction_id?: string | null
          external_payment_id?: string | null
          payment_data?: Json | null
          paid_at?: string | null
          refunded_at?: string | null
          refund_amount?: number | null
          failure_reason?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      generate_order_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_merchant_stats: {
        Args: {
          merchant_uuid: string
        }
        Returns: Json
      }
      search_products: {
        Args: {
          merchant_uuid: string
          search_query?: string
          category_uuid?: string
          limit_count?: number
          offset_count?: number
        }
        Returns: {
          id: string
          name: string
          description: string
          price: number
          stock_quantity: number
          images: string[]
          category_name: string
        }[]
      }
    }
    Enums: {
      business_type: BusinessType
      order_status: OrderStatus
      payment_status: PaymentStatus
      payment_method: PaymentMethod
      user_role: UserRole
      merchant_status: MerchantStatus
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
