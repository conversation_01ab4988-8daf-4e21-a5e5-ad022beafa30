export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
            柒零支付连锁
          </h1>
          <p className="text-xl md:text-2xl text-gray-600 mb-8">
            商家服务平台
          </p>
          <p className="text-lg text-gray-500 mb-12 max-w-2xl mx-auto">
            基于 Next.js 15 + Supabase 的现代全栈架构，支持酒店、餐饮、商品管理和多支付方式集成
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="text-3xl mb-4">🏨</div>
              <h3 className="text-xl font-semibold mb-2">酒店管理</h3>
              <p className="text-gray-600">房间管理、价格设置、预订处理</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="text-3xl mb-4">🍽️</div>
              <h3 className="text-xl font-semibold mb-2">餐饮管理</h3>
              <p className="text-gray-600">菜单管理、营业时间、订餐服务</p>
            </div>
            
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="text-3xl mb-4">🛍️</div>
              <h3 className="text-xl font-semibold mb-2">商品管理</h3>
              <p className="text-gray-600">库存管理、商品分类、价格设置</p>
            </div>
          </div>
          
          <div className="mt-12">
            <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
              <div className="text-3xl mb-4">💳</div>
              <h3 className="text-xl font-semibold mb-2">柒零支付</h3>
              <p className="text-gray-600">支持柒零支付、支付宝、微信支付</p>
            </div>
          </div>
          
          <div className="mt-16">
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <a
                href="/login"
                className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 transition-colors"
              >
                🔑 登录账户
              </a>
              <a
                href="/register"
                className="inline-flex items-center px-6 py-3 border border-blue-600 text-base font-medium rounded-md text-blue-600 bg-white hover:bg-blue-50 transition-colors"
              >
                ✨ 免费注册
              </a>
              <a
                href="/dashboard"
                className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors"
              >
                📊 管理后台
              </a>
            </div>

            <div className="flex justify-center mb-8">
              <a
                href="/test-db"
                className="inline-flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-800 transition-colors"
              >
                🔗 测试数据库连接
              </a>
            </div>
            <p className="text-sm text-gray-400">
              项目初始化完成 ✅ | 数据库配置完成 ✅ | 用户认证系统完成 ✅ | Next.js 15 + TypeScript + Tailwind CSS
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
