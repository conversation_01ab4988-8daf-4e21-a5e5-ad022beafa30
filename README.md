# 柒零支付连锁 - 商家服务平台

基于 Next.js 15 + Supabase 的现代全栈商家管理平台，支持酒店、餐饮、商品管理和多支付方式集成。

## 🚀 技术栈

- **前端**: Next.js 15 (App Router) + TypeScript + Tailwind CSS
- **后端**: Supabase (PostgreSQL + Auth + Storage + Edge Functions)
- **UI组件**: shadcn/ui
- **状态管理**: React Context + Zustand (按需)
- **表单处理**: React Hook Form + Zod
- **部署**: Vercel + Supabase Cloud

## 📋 功能模块

### 🏨 酒店管理
- 酒店信息管理
- 房间类型设置
- 价格管理
- 房间状态跟踪

### 🍽️ 餐饮管理
- 餐厅信息管理
- 菜单管理
- 营业时间设置
- 订餐功能

### 🛍️ 商品管理
- 商品信息管理
- 库存管理
- 价格设置
- 商品分类

### 💳 支付系统
- 柒零支付（内部）
- 支付宝集成
- 微信支付集成
- 统一支付网关

### 📊 数据统计
- 销售统计
- 订单分析
- 收入报表
- 业务数据可视化

## 🛠️ 开发环境设置

### 环境要求
- Node.js 18+
- npm 或 yarn
- Git

### 安装依赖
```bash
npm install
```

### 环境变量配置
复制 `.env.example` 到 `.env.local` 并填入相应的配置：

```bash
cp .env.example .env.local
```

### Supabase 数据库配置

#### 1. 创建 Supabase 项目
1. 访问 [Supabase](https://supabase.com) 并创建新项目
2. 获取项目 URL 和 API 密钥
3. 将配置信息添加到 `.env.local` 文件

#### 2. 运行数据库迁移
```bash
# 如果使用本地 Supabase（推荐用于开发）
npx supabase start
npx supabase db reset

# 如果使用云端 Supabase
# 手动在 Supabase Dashboard 的 SQL Editor 中运行以下文件：
# - supabase/migrations/20250701000001_initial_schema.sql
# - supabase/migrations/20250701000002_rls_policies.sql
# - supabase/migrations/20250701000003_functions_triggers.sql
```

#### 3. 添加示例数据（可选）
```bash
# 在 Supabase SQL Editor 中运行
# supabase/seed.sql
```

#### 4. 测试数据库连接
启动开发服务器后，访问 `/test-db` 页面验证数据库连接是否正常。

### 启动开发服务器
```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📁 项目结构

```
├── app/                          # Next.js 15 App Router
│   ├── (auth)/                   # 认证路由组
│   │   ├── login/
│   │   └── register/
│   ├── (dashboard)/              # 主要业务路由组
│   │   ├── hotels/               # 酒店管理
│   │   ├── restaurants/          # 餐饮管理
│   │   ├── products/             # 商品管理
│   │   ├── orders/               # 订单管理
│   │   ├── payments/             # 支付管理
│   │   └── analytics/            # 数据统计
│   ├── api/                      # API路由
│   ├── globals.css
│   ├── layout.tsx
│   └── page.tsx
├── components/                   # 可复用组件
│   ├── ui/                       # shadcn/ui组件
│   ├── forms/                    # 表单组件
│   ├── layouts/                  # 布局组件
│   └── charts/                   # 图表组件
├── lib/                          # 工具函数
│   ├── supabase/                 # Supabase客户端
│   ├── utils.ts                  # 通用工具
│   ├── validations.ts            # Zod验证模式
│   └── constants.ts              # 常量定义
├── types/                        # TypeScript类型定义
├── hooks/                        # 自定义React Hooks
└── public/                       # 静态资源
```

## 🔧 可用脚本

- `npm run dev` - 启动开发服务器
- `npm run build` - 构建生产版本
- `npm run start` - 启动生产服务器
- `npm run lint` - 运行ESLint检查
- `npm run type-check` - 运行TypeScript类型检查

## 📝 开发状态

### ✅ 已完成
- [x] 项目初始化和基础架构搭建
- [x] Next.js 15 + TypeScript + Tailwind CSS 配置
- [x] 基础目录结构创建
- [x] 环境变量模板
- [x] 基础工具函数和类型定义
- [x] Supabase数据库设计和配置
- [x] 数据库表结构设计（商家、酒店、餐厅、商品、订单、支付）
- [x] Row Level Security (RLS) 策略配置
- [x] TypeScript类型定义生成
- [x] Supabase客户端工具函数
- [x] 数据库查询封装
- [x] 中间件认证配置

### 🚧 进行中
- [ ] 用户认证系统开发
- [ ] 基础UI组件库和布局系统

### 📋 待开发
- [ ] 商家信息管理模块
- [ ] 酒店管理模块
- [ ] 订单管理系统
- [ ] 支付系统集成
- [ ] 餐饮管理模块
- [ ] 商品管理模块
- [ ] 数据统计和报表
- [ ] 移动端优化
- [ ] 性能优化和安全加固

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目地址: [GitHub Repository]
- 问题反馈: [GitHub Issues]

---

**柒零支付连锁** - 让商家管理更简单 🚀
