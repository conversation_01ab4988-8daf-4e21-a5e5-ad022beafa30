/**
 * 数据统计主页面
 * 显示业务数据分析和报表
 */

'use client'

import { useState, useEffect } from 'react'
import { Calendar, Download, Filter, RefreshCw, TrendingUp, BarChart3, <PERSON><PERSON><PERSON>, Activity } from 'lucide-react'
import { PageContainer } from '@/components/layouts/page-container'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { KPIGrid, DEFAULT_KPI_METRICS, getBusinessTypeKPIs } from '@/components/analytics/kpi-cards'
import { <PERSON><PERSON>hart, <PERSON><PERSON>hart, <PERSON><PERSON><PERSON> as PieChartComponent, AreaChart, GaugeChart } from '@/components/analytics/chart-components'
import { useAuth } from '@/lib/auth/context'
import { getAnalyticsReport } from '@/lib/actions/analytics'
import type { AnalyticsReport, TimeRange, KPIMetric } from '@/types/analytics'

export default function AnalyticsPage() {
  const { user } = useAuth()
  const [timeRange, setTimeRange] = useState<TimeRange>('last_30_days')
  const [isLoading, setIsLoading] = useState(true)
  const [report, setReport] = useState<AnalyticsReport | null>(null)
  const [error, setError] = useState('')
  const [kpiMetrics, setKpiMetrics] = useState<KPIMetric[]>(DEFAULT_KPI_METRICS)

  useEffect(() => {
    loadAnalyticsData()
  }, [timeRange])

  useEffect(() => {
    // 根据商家业务类型设置KPI指标
    if (user?.merchant?.business_types) {
      const businessType = user.merchant.business_types[0] // 取第一个业务类型
      const metrics = getBusinessTypeKPIs(businessType)
      setKpiMetrics(metrics)
    }
  }, [user])

  const loadAnalyticsData = async () => {
    setIsLoading(true)
    setError('')

    try {
      const result = await getAnalyticsReport({
        time_range: timeRange,
        include_trends: true,
        compare_previous: true,
      })

      if (result.success && result.data) {
        setReport(result.data as AnalyticsReport)
        
        // 更新KPI指标数据
        const updatedMetrics = kpiMetrics.map(metric => {
          switch (metric.id) {
            case 'revenue':
              return {
                ...metric,
                value: result.data.summary.total_revenue,
                change: result.data.revenue.growth,
                change_percentage: result.data.revenue.growth_percentage,
              }
            case 'orders':
              return {
                ...metric,
                value: result.data.summary.total_orders,
                change: result.data.orders.growth,
                change_percentage: result.data.orders.growth_percentage,
              }
            case 'customers':
              return {
                ...metric,
                value: result.data.summary.total_customers,
                change: result.data.customers.growth,
                change_percentage: result.data.customers.growth_percentage,
              }
            default:
              return metric
          }
        })
        setKpiMetrics(updatedMetrics)
      } else {
        setError(result.error || '获取分析数据失败')
      }
    } catch (err) {
      setError('获取分析数据失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  const handleExportData = () => {
    // 模拟数据导出
    const csvData = [
      ['日期', '收入', '订单数', '客户数'],
      ...report?.trends.daily_sales.map(item => [
        item.date,
        item.value,
        Math.floor(item.value / 100),
        Math.floor(item.value / 150)
      ]) || []
    ]
    
    const csvContent = csvData.map(row => row.join(',')).join('\n')
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)
    link.setAttribute('href', url)
    link.setAttribute('download', `analytics_${timeRange}_${new Date().toISOString().split('T')[0]}.csv`)
    link.style.visibility = 'hidden'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toLocaleString()}`
  }

  const getTimeRangeLabel = (range: TimeRange) => {
    const labels = {
      today: '今天',
      yesterday: '昨天',
      last_7_days: '最近7天',
      last_30_days: '最近30天',
      last_90_days: '最近90天',
      this_month: '本月',
      last_month: '上月',
      this_year: '今年',
      custom: '自定义',
    }
    return labels[range] || '最近30天'
  }

  if (isLoading) {
    return (
      <PageContainer
        title="数据统计"
        description="业务数据分析和报表"
        breadcrumbs={[
          { title: '仪表板', href: '/dashboard' },
          { title: '数据统计' }
        ]}
      >
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </PageContainer>
    )
  }

  if (error) {
    return (
      <PageContainer
        title="数据统计"
        description="业务数据分析和报表"
        breadcrumbs={[
          { title: '仪表板', href: '/dashboard' },
          { title: '数据统计' }
        ]}
      >
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-destructive mb-4">{error}</p>
              <Button onClick={loadAnalyticsData}>重新加载</Button>
            </div>
          </CardContent>
        </Card>
      </PageContainer>
    )
  }

  return (
    <PageContainer
      title="数据统计"
      description="业务数据分析和报表"
      breadcrumbs={[
        { title: '仪表板', href: '/dashboard' },
        { title: '数据统计' }
      ]}
    >
      <div className="space-y-6">
        {/* 控制栏 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Select value={timeRange} onValueChange={(value: TimeRange) => setTimeRange(value)}>
              <SelectTrigger className="w-40">
                <Calendar className="h-4 w-4 mr-2" />
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">今天</SelectItem>
                <SelectItem value="yesterday">昨天</SelectItem>
                <SelectItem value="last_7_days">最近7天</SelectItem>
                <SelectItem value="last_30_days">最近30天</SelectItem>
                <SelectItem value="last_90_days">最近90天</SelectItem>
                <SelectItem value="this_month">本月</SelectItem>
                <SelectItem value="last_month">上月</SelectItem>
                <SelectItem value="this_year">今年</SelectItem>
              </SelectContent>
            </Select>

            <Badge variant="outline" className="text-sm">
              {getTimeRangeLabel(timeRange)}
            </Badge>
          </div>

          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" onClick={loadAnalyticsData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新
            </Button>
            <Button variant="outline" size="sm" onClick={handleExportData}>
              <Download className="h-4 w-4 mr-2" />
              导出
            </Button>
          </div>
        </div>

        {/* KPI指标卡片 */}
        <KPIGrid metrics={kpiMetrics} />

        {/* 图表区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 销售趋势图 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>销售趋势</span>
              </CardTitle>
              <CardDescription>每日销售额变化趋势</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                {report?.trends.daily_sales && (
                  <LineChart
                    data={report.trends.daily_sales}
                    width={400}
                    height={200}
                    color="#3b82f6"
                    showGrid={true}
                    showDots={true}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* 收入分析 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>收入分析</span>
              </CardTitle>
              <CardDescription>按支付方式分类的收入</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                {report?.revenue.revenue_by_payment_method && (
                  <BarChart
                    data={Object.entries(report.revenue.revenue_by_payment_method).map(([method, amount]) => ({
                      label: method,
                      value: amount,
                      color: method === 'qiling_pay' ? '#3b82f6' : 
                             method === 'alipay' ? '#1677ff' :
                             method === 'wechat_pay' ? '#07c160' : '#64748b'
                    }))}
                    width={400}
                    height={200}
                    showValues={true}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* 订单状态分布 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <PieChart className="h-5 w-5" />
                <span>订单状态分布</span>
              </CardTitle>
              <CardDescription>各状态订单数量占比</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex justify-center">
                {report?.orders.orders_by_status && (
                  <PieChartComponent
                    data={Object.entries(report.orders.orders_by_status).map(([status, count]) => ({
                      label: status,
                      value: count,
                    }))}
                    width={300}
                    height={200}
                    showLabels={true}
                    showPercentages={true}
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* 业务表现仪表盘 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>业务表现</span>
              </CardTitle>
              <CardDescription>综合业务表现评分</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex justify-center items-center">
                <GaugeChart
                  value={75}
                  max={100}
                  min={0}
                  width={250}
                  height={150}
                  color="#10b981"
                  label="综合评分"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 业务洞察 */}
        {report?.insights && report.insights.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle>业务洞察</CardTitle>
              <CardDescription>基于数据分析的业务建议</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {report.insights.map((insight, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded-lg border-l-4 ${
                      insight.type === 'positive' ? 'border-green-500 bg-green-50' :
                      insight.type === 'negative' ? 'border-red-500 bg-red-50' :
                      'border-blue-500 bg-blue-50'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium">{insight.title}</h4>
                        <p className="text-sm text-muted-foreground mt-1">{insight.description}</p>
                        {insight.recommendation && (
                          <p className="text-sm font-medium mt-2">
                            💡 建议：{insight.recommendation}
                          </p>
                        )}
                      </div>
                      <Badge variant={insight.impact === 'high' ? 'destructive' : insight.impact === 'medium' ? 'warning' : 'secondary'}>
                        {insight.impact === 'high' ? '高影响' : insight.impact === 'medium' ? '中影响' : '低影响'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </PageContainer>
  )
}
