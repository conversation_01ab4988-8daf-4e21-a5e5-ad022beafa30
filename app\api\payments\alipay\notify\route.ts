/**
 * 支付宝支付回调API
 * 处理支付宝的异步通知
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { paymentGateway } from '@/lib/payments/payment-gateway'

// POST 处理支付宝异步通知
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const data: Record<string, string> = {}
    
    // 将FormData转换为对象
    formData.forEach((value, key) => {
      data[key] = value.toString()
    })

    console.log('Alipay callback received:', data)

    // 验证回调数据
    const verifyResult = await paymentGateway.verifyCallback('alipay', data)

    if (!verifyResult.success) {
      console.error('Alipay callback verification failed:', verifyResult.error)
      return new Response('fail', { status: 400 })
    }

    const { paymentId, status, amount } = verifyResult

    if (!paymentId || !status) {
      console.error('Alipay callback data incomplete:', { paymentId, status })
      return new Response('fail', { status: 400 })
    }

    // 更新支付状态
    const supabase = createClient()
    
    // 查找支付记录
    const { data: payment, error: findError } = await supabase
      .from('payments')
      .select('id, status, order_id, merchant_id')
      .eq('payment_id', paymentId)
      .single()

    if (findError || !payment) {
      console.error('Payment not found:', paymentId, findError)
      return new Response('fail', { status: 404 })
    }

    // 如果状态已经是最终状态，不需要更新
    if (['completed', 'failed', 'cancelled', 'refunded'].includes(payment.status)) {
      console.log('Payment status is already final:', payment.status)
      return new Response('success')
    }

    // 更新支付状态
    const updateData: any = {
      status,
      updated_at: new Date().toISOString(),
    }

    if (status === 'completed') {
      updateData.paid_amount = amount
      updateData.paid_at = new Date().toISOString()
    }

    const { error: updateError } = await supabase
      .from('payments')
      .update(updateData)
      .eq('id', payment.id)

    if (updateError) {
      console.error('Update payment status error:', updateError)
      return new Response('fail', { status: 500 })
    }

    // 如果支付成功，更新订单状态
    if (status === 'completed') {
      const { error: orderUpdateError } = await supabase
        .from('orders')
        .update({
          payment_status: 'paid',
          status: 'processing', // 支付成功后订单进入处理状态
          updated_at: new Date().toISOString(),
        })
        .eq('id', payment.order_id)

      if (orderUpdateError) {
        console.error('Update order status error:', orderUpdateError)
        // 不返回错误，因为支付状态已经更新成功
      }
    }

    // 记录回调日志
    await supabase
      .from('payment_logs')
      .insert({
        payment_id: payment.id,
        action: 'alipay_callback',
        status,
        data: data,
        created_at: new Date().toISOString(),
      })

    console.log(`Alipay callback processed: ${paymentId} -> ${status}`)

    return new Response('success')

  } catch (error) {
    console.error('Alipay callback error:', error)
    return new Response('fail', { status: 500 })
  }
}

// GET 处理支付宝同步返回
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    
    // 将查询参数转换为对象
    const params: Record<string, string> = {}
    searchParams.forEach((value, key) => {
      params[key] = value
    })

    console.log('Alipay return received:', params)

    // 验证回调数据
    const verifyResult = await paymentGateway.verifyCallback('alipay', params)

    if (!verifyResult.success) {
      console.error('Alipay return verification failed:', verifyResult.error)
      return NextResponse.redirect(new URL('/payments/failed', request.url))
    }

    const { paymentId, status } = verifyResult

    if (!paymentId) {
      return NextResponse.redirect(new URL('/payments/failed', request.url))
    }

    // 根据支付状态重定向
    if (status === 'completed') {
      return NextResponse.redirect(new URL(`/payments/success?payment_id=${paymentId}`, request.url))
    } else if (status === 'cancelled') {
      return NextResponse.redirect(new URL(`/payments/cancelled?payment_id=${paymentId}`, request.url))
    } else {
      return NextResponse.redirect(new URL(`/payments/pending?payment_id=${paymentId}`, request.url))
    }

  } catch (error) {
    console.error('Alipay return error:', error)
    return NextResponse.redirect(new URL('/payments/failed', request.url))
  }
}

// OPTIONS 处理预检请求
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  })
}
