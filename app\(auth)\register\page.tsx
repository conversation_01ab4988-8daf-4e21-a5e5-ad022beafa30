/**
 * 注册页面
 * 用户注册界面
 */

'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { AuthForm } from '@/components/forms/auth-form'
import { register } from '@/lib/auth/actions'
import type { RegisterFormData } from '@/lib/validations/auth'

export default function RegisterPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [needsVerification, setNeedsVerification] = useState(false)
  const router = useRouter()

  const handleRegister = async (data: RegisterFormData) => {
    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      const result = await register(data)

      if (result.success) {
        setSuccess(result.message || '注册成功')
        setNeedsVerification(result.needsVerification || false)
        
        // 如果不需要验证，直接跳转到登录页
        if (!result.needsVerification) {
          setTimeout(() => {
            router.push('/login')
          }, 2000)
        }
      } else {
        setError(result.error || '注册失败')
      }
    } catch (err) {
      setError('注册失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        {/* 头部 */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            创建账户
          </h1>
          <p className="text-gray-600">
            加入柒零支付连锁管理平台
          </p>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* 成功提示 */}
        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-600">{success}</p>
                {needsVerification && (
                  <p className="text-sm text-green-600 mt-2">
                    请检查您的邮箱并点击验证链接以激活账户。
                  </p>
                )}
              </div>
            </div>
          </div>
        )}

        {/* 注册表单 */}
        {!success && (
          <AuthForm
            type="register"
            onSubmit={handleRegister}
            isLoading={isLoading}
          />
        )}

        {/* 成功后的操作 */}
        {success && (
          <div className="space-y-4">
            <div className="text-center">
              <Link
                href="/login"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                前往登录
              </Link>
            </div>
            
            {needsVerification && (
              <div className="text-center">
                <p className="text-sm text-gray-600 mb-2">
                  没有收到验证邮件？
                </p>
                <button
                  type="button"
                  className="text-sm text-blue-600 hover:text-blue-500"
                  onClick={() => {
                    // 这里可以添加重新发送验证邮件的逻辑
                    console.log('重新发送验证邮件')
                  }}
                >
                  重新发送验证邮件
                </button>
              </div>
            )}
          </div>
        )}

        {/* 底部链接 */}
        {!success && (
          <div className="mt-6 text-center">
            <span className="text-sm text-gray-600">
              已有账户？{' '}
              <Link
                href="/login"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                立即登录
              </Link>
            </span>
          </div>
        )}
      </div>

      {/* 返回首页 */}
      <div className="mt-6 text-center">
        <Link
          href="/"
          className="text-sm text-gray-500 hover:text-gray-700"
        >
          ← 返回首页
        </Link>
      </div>
    </div>
  )
}
