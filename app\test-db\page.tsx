/**
 * 数据库连接测试页面
 * 用于验证 Supabase 配置是否正确
 */

import { createClient } from '@/lib/supabase/server'

export default async function TestDatabasePage() {
  const supabase = createClient()
  
  // 测试数据库连接
  let connectionStatus = 'unknown'
  let merchants = []
  let error = null

  try {
    // 尝试查询商家表
    const { data, error: queryError } = await supabase
      .from('merchants')
      .select('id, name, business_types, status, created_at')
      .limit(5)

    if (queryError) {
      throw queryError
    }

    connectionStatus = 'success'
    merchants = data || []
  } catch (err: any) {
    connectionStatus = 'error'
    error = err.message
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            数据库连接测试
          </h1>

          {/* 连接状态 */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">连接状态</h2>
            <div className="flex items-center space-x-3">
              {connectionStatus === 'success' && (
                <>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-green-700 font-medium">数据库连接成功</span>
                </>
              )}
              {connectionStatus === 'error' && (
                <>
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <span className="text-red-700 font-medium">数据库连接失败</span>
                </>
              )}
              {connectionStatus === 'unknown' && (
                <>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <span className="text-yellow-700 font-medium">连接状态未知</span>
                </>
              )}
            </div>
            
            {error && (
              <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                <h3 className="text-sm font-medium text-red-800 mb-2">错误信息：</h3>
                <p className="text-sm text-red-700 font-mono">{error}</p>
              </div>
            )}
          </div>

          {/* 数据库信息 */}
          <div className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">数据库信息</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Supabase URL
                </label>
                <p className="text-sm text-gray-600 font-mono bg-gray-50 p-2 rounded">
                  {process.env.NEXT_PUBLIC_SUPABASE_URL || '未配置'}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  匿名密钥状态
                </label>
                <p className="text-sm text-gray-600">
                  {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '已配置' : '未配置'}
                </p>
              </div>
            </div>
          </div>

          {/* 示例数据 */}
          {connectionStatus === 'success' && (
            <div className="bg-white rounded-lg shadow-sm p-6">
              <h2 className="text-xl font-semibold mb-4">
                商家数据示例 ({merchants.length} 条记录)
              </h2>
              
              {merchants.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          商家名称
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          业务类型
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          状态
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          创建时间
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {merchants.map((merchant: any) => (
                        <tr key={merchant.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                            {merchant.name}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {merchant.business_types?.join(', ') || '无'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              merchant.status === 'active' 
                                ? 'bg-green-100 text-green-800'
                                : 'bg-gray-100 text-gray-800'
                            }`}>
                              {merchant.status}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {new Date(merchant.created_at).toLocaleDateString('zh-CN')}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <p className="text-gray-500">暂无商家数据</p>
                  <p className="text-sm text-gray-400 mt-2">
                    请运行种子数据脚本来添加示例数据
                  </p>
                </div>
              )}
            </div>
          )}

          {/* 配置说明 */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mt-6">
            <h3 className="text-lg font-medium text-blue-900 mb-3">配置说明</h3>
            <div className="text-sm text-blue-800 space-y-2">
              <p>1. 确保已创建 Supabase 项目并获取 URL 和 API 密钥</p>
              <p>2. 将配置信息添加到 <code className="bg-blue-100 px-1 rounded">.env.local</code> 文件中</p>
              <p>3. 运行数据库迁移脚本创建表结构</p>
              <p>4. 可选：运行种子数据脚本添加示例数据</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
