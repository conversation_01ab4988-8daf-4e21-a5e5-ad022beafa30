/**
 * 商品列表组件
 * 显示商品列表，支持搜索和筛选
 */

'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { MoreHorizontal, Edit, Trash2, Eye, Package, Star, Tag } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { deleteProduct } from '@/lib/actions/products'
import { 
  PRODUCT_STATUS_CONFIG, 
  PRODUCT_TYPE_CONFIG,
  STOCK_STATUS_CONFIG,
  type ProductWithDetails
} from '@/types/product'

interface ProductListProps {
  products: ProductWithDetails[]
  onProductDeleted?: () => void
}

export function ProductList({ products, onProductDeleted }: ProductListProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [isDeleting, setIsDeleting] = useState<string | null>(null)

  // 过滤商品
  const filteredProducts = products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.sku?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.brand?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleDelete = async (productId: string, productName: string) => {
    if (!confirm(`确定要删除商品"${productName}"吗？此操作不可撤销。`)) {
      return
    }

    setIsDeleting(productId)

    try {
      const result = await deleteProduct(productId)

      if (result.success) {
        onProductDeleted?.()
      } else {
        alert(result.error || '删除失败')
      }
    } catch (error) {
      alert('删除失败，请稍后重试')
    } finally {
      setIsDeleting(null)
    }
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`
  }

  const getStockStatus = (quantity: number, reorderPoint: number = 10) => {
    if (quantity <= 0) return 'out_of_stock'
    if (quantity <= reorderPoint) return 'low_stock'
    return 'in_stock'
  }

  const getStockStatusConfig = (quantity: number, reorderPoint?: number) => {
    const status = getStockStatus(quantity, reorderPoint)
    return STOCK_STATUS_CONFIG[status]
  }

  return (
    <div className="space-y-6">
      {/* 搜索栏 */}
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <Input
            placeholder="搜索商品名称、SKU、品牌或描述..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Link href="/products/new">
          <Button>添加商品</Button>
        </Link>
      </div>

      {/* 商品列表 */}
      {filteredProducts.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <Package className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground mb-4">
                {searchQuery ? '没有找到匹配的商品' : '还没有添加任何商品'}
              </p>
              {!searchQuery && (
                <Link href="/products/new">
                  <Button>添加第一个商品</Button>
                </Link>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredProducts.map((product) => {
            const statusConfig = PRODUCT_STATUS_CONFIG[product.status as keyof typeof PRODUCT_STATUS_CONFIG]
            const typeConfig = PRODUCT_TYPE_CONFIG[product.product_type as keyof typeof PRODUCT_TYPE_CONFIG]
            const stockConfig = getStockStatusConfig(product.stock_quantity || 0)

            return (
              <Card key={product.id} className="overflow-hidden">
                {/* 商品图片 */}
                <div className="relative h-48 bg-muted">
                  {product.images && product.images.length > 0 ? (
                    <Image
                      src={product.images[0]}
                      alt={product.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <Package className="h-12 w-12 text-muted-foreground" />
                    </div>
                  )}
                  
                  {/* 状态标识 */}
                  <div className="absolute top-2 right-2 space-y-1">
                    <Badge variant={product.is_active ? 'success' : 'secondary'}>
                      {statusConfig?.icon} {statusConfig?.label}
                    </Badge>
                    {product.is_featured && (
                      <Badge variant="warning" className="block">
                        ⭐ 推荐
                      </Badge>
                    )}
                  </div>

                  {/* 库存状态 */}
                  <div className="absolute top-2 left-2">
                    <Badge 
                      variant={
                        stockConfig.color === 'green' ? 'success' :
                        stockConfig.color === 'yellow' ? 'warning' : 'destructive'
                      }
                      className="text-xs"
                    >
                      {stockConfig.icon} {product.stock_quantity || 0}
                    </Badge>
                  </div>
                </div>

                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg line-clamp-1">{product.name}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        {product.sku && (
                          <span className="text-xs text-muted-foreground">SKU: {product.sku}</span>
                        )}
                        {product.brand && (
                          <Badge variant="outline" className="text-xs">
                            {product.brand}
                          </Badge>
                        )}
                      </div>
                    </div>
                    
                    {/* 评分 */}
                    {product.average_rating && product.average_rating > 0 && (
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <span className="text-sm font-medium">{product.average_rating}</span>
                      </div>
                    )}
                  </div>
                </CardHeader>

                <CardContent className="space-y-4">
                  {/* 商品描述 */}
                  {product.short_description && (
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {product.short_description}
                    </p>
                  )}

                  {/* 价格信息 */}
                  <div className="space-y-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg font-bold text-primary">
                        {formatCurrency(product.sale_price || product.base_price)}
                      </span>
                      {product.sale_price && product.sale_price < product.base_price && (
                        <span className="text-sm text-muted-foreground line-through">
                          {formatCurrency(product.base_price)}
                        </span>
                      )}
                    </div>
                    {product.cost_price && (
                      <div className="text-xs text-muted-foreground">
                        成本: {formatCurrency(product.cost_price)}
                      </div>
                    )}
                  </div>

                  {/* 商品类型和分类 */}
                  <div className="flex flex-wrap gap-1">
                    <Badge variant="outline" className="text-xs">
                      {typeConfig?.icon} {typeConfig?.label}
                    </Badge>
                    {product.category && (
                      <Badge variant="outline" className="text-xs">
                        <Tag className="h-3 w-3 mr-1" />
                        {product.category.name}
                      </Badge>
                    )}
                  </div>

                  {/* 标签 */}
                  {product.tags && product.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {product.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          #{tag}
                        </Badge>
                      ))}
                      {product.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{product.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                  )}

                  {/* 商品统计 */}
                  <div className="flex items-center justify-between text-sm text-muted-foreground">
                    <span>库存: {product.stock_quantity || 0}</span>
                    <span>变体: {product.variants?.length || 0}</span>
                    {product.total_sales && (
                      <span>销量: {product.total_sales}</span>
                    )}
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex items-center justify-between pt-2">
                    <div className="flex items-center space-x-2">
                      <Link href={`/products/${product.id}`}>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          查看
                        </Button>
                      </Link>
                      <Link href={`/products/${product.id}/edit`}>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-1" />
                          编辑
                        </Button>
                      </Link>
                    </div>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(product.id, product.name)}
                      disabled={isDeleting === product.id}
                      className="text-destructive hover:text-destructive"
                    >
                      {isDeleting === product.id ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-destructive" />
                      ) : (
                        <Trash2 className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}
    </div>
  )
}
