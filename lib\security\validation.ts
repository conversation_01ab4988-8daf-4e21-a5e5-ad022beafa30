/**
 * 安全验证工具
 * 提供输入验证、数据清理和安全检查功能
 */

import crypto from 'crypto'
import { z } from 'zod'

// 常用的验证模式
export const ValidationSchemas = {
  // 邮箱验证
  email: z.string().email('请输入有效的邮箱地址'),
  
  // 手机号验证（中国大陆）
  phone: z.string().regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码'),
  
  // 密码验证（至少8位，包含字母和数字）
  password: z.string()
    .min(8, '密码至少需要8位字符')
    .regex(/^(?=.*[A-Za-z])(?=.*\d)/, '密码必须包含字母和数字'),
  
  // 用户名验证
  username: z.string()
    .min(2, '用户名至少需要2个字符')
    .max(20, '用户名不能超过20个字符')
    .regex(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, '用户名只能包含字母、数字、下划线和中文'),
  
  // 金额验证
  amount: z.number()
    .positive('金额必须大于0')
    .max(999999.99, '金额不能超过999999.99'),
  
  // URL验证
  url: z.string().url('请输入有效的URL'),
  
  // ID验证（UUID格式）
  uuid: z.string().uuid('请输入有效的ID'),
  
  // 商家名称验证
  merchantName: z.string()
    .min(2, '商家名称至少需要2个字符')
    .max(50, '商家名称不能超过50个字符'),
  
  // 地址验证
  address: z.string()
    .min(5, '地址至少需要5个字符')
    .max(200, '地址不能超过200个字符'),
}

// XSS防护 - HTML实体编码
export function escapeHtml(unsafe: string): string {
  return unsafe
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;')
}

// SQL注入防护 - 清理SQL输入
export function sanitizeSqlInput(input: string): string {
  // 移除或转义危险字符
  return input
    .replace(/['"\\;]/g, '') // 移除引号、反斜杠、分号
    .replace(/--/g, '') // 移除SQL注释
    .replace(/\/\*/g, '') // 移除多行注释开始
    .replace(/\*\//g, '') // 移除多行注释结束
    .trim()
}

// 文件名安全检查
export function sanitizeFileName(fileName: string): string {
  return fileName
    .replace(/[^a-zA-Z0-9._-]/g, '_') // 只保留安全字符
    .replace(/\.{2,}/g, '.') // 防止目录遍历
    .substring(0, 255) // 限制长度
}

// 路径遍历攻击防护
export function sanitizePath(path: string): string {
  return path
    .replace(/\.\./g, '') // 移除父目录引用
    .replace(/[<>:"|?*]/g, '') // 移除Windows非法字符
    .replace(/^\/+/, '') // 移除开头的斜杠
}

// CSRF Token生成和验证
export class CSRFProtection {
  private static readonly SECRET_KEY = process.env.CSRF_SECRET || 'default-csrf-secret'
  
  static generateToken(sessionId: string): string {
    const timestamp = Date.now().toString()
    const data = `${sessionId}:${timestamp}`
    const signature = crypto
      .createHmac('sha256', this.SECRET_KEY)
      .update(data)
      .digest('hex')
    
    return Buffer.from(`${data}:${signature}`).toString('base64')
  }
  
  static verifyToken(token: string, sessionId: string, maxAge: number = 3600000): boolean {
    try {
      const decoded = Buffer.from(token, 'base64').toString()
      const [receivedSessionId, timestamp, signature] = decoded.split(':')
      
      // 验证会话ID
      if (receivedSessionId !== sessionId) {
        return false
      }
      
      // 验证时间戳
      const tokenAge = Date.now() - parseInt(timestamp)
      if (tokenAge > maxAge) {
        return false
      }
      
      // 验证签名
      const data = `${receivedSessionId}:${timestamp}`
      const expectedSignature = crypto
        .createHmac('sha256', this.SECRET_KEY)
        .update(data)
        .digest('hex')
      
      return signature === expectedSignature
    } catch {
      return false
    }
  }
}

// 密码哈希和验证
export class PasswordSecurity {
  private static readonly SALT_ROUNDS = 12
  
  static async hashPassword(password: string): Promise<string> {
    const bcrypt = await import('bcryptjs')
    return bcrypt.hash(password, this.SALT_ROUNDS)
  }
  
  static async verifyPassword(password: string, hash: string): Promise<boolean> {
    const bcrypt = await import('bcryptjs')
    return bcrypt.compare(password, hash)
  }
  
  static generateSecurePassword(length: number = 16): string {
    const charset = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789!@#$%^&*'
    let password = ''
    
    for (let i = 0; i < length; i++) {
      const randomIndex = crypto.randomInt(0, charset.length)
      password += charset[randomIndex]
    }
    
    return password
  }
}

// 数据加密和解密
export class DataEncryption {
  private static readonly ALGORITHM = 'aes-256-gcm'
  private static readonly KEY = crypto.scryptSync(
    process.env.ENCRYPTION_KEY || 'default-encryption-key',
    'salt',
    32
  )
  
  static encrypt(text: string): string {
    const iv = crypto.randomBytes(16)
    const cipher = crypto.createCipher(this.ALGORITHM, this.KEY)
    
    let encrypted = cipher.update(text, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    
    const authTag = cipher.getAuthTag()
    
    return `${iv.toString('hex')}:${authTag.toString('hex')}:${encrypted}`
  }
  
  static decrypt(encryptedData: string): string {
    const [ivHex, authTagHex, encrypted] = encryptedData.split(':')
    
    const iv = Buffer.from(ivHex, 'hex')
    const authTag = Buffer.from(authTagHex, 'hex')
    
    const decipher = crypto.createDecipher(this.ALGORITHM, this.KEY)
    decipher.setAuthTag(authTag)
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  }
}

// 速率限制
export class RateLimiter {
  private static requests = new Map<string, { count: number; resetTime: number }>()
  
  static isAllowed(
    identifier: string,
    maxRequests: number = 100,
    windowMs: number = 60000
  ): boolean {
    const now = Date.now()
    const record = this.requests.get(identifier)
    
    if (!record || now > record.resetTime) {
      // 新的时间窗口
      this.requests.set(identifier, {
        count: 1,
        resetTime: now + windowMs,
      })
      return true
    }
    
    if (record.count >= maxRequests) {
      return false
    }
    
    record.count++
    return true
  }
  
  static getRemainingRequests(identifier: string, maxRequests: number = 100): number {
    const record = this.requests.get(identifier)
    if (!record || Date.now() > record.resetTime) {
      return maxRequests
    }
    return Math.max(0, maxRequests - record.count)
  }
  
  static cleanup(): void {
    const now = Date.now()
    for (const [key, record] of this.requests.entries()) {
      if (now > record.resetTime) {
        this.requests.delete(key)
      }
    }
  }
}

// IP地址验证和地理位置检查
export class IPSecurity {
  private static readonly BLOCKED_IPS = new Set<string>()
  private static readonly SUSPICIOUS_PATTERNS = [
    /^10\./, // 私有IP
    /^192\.168\./, // 私有IP
    /^172\.(1[6-9]|2[0-9]|3[01])\./, // 私有IP
  ]
  
  static isBlocked(ip: string): boolean {
    return this.BLOCKED_IPS.has(ip)
  }
  
  static blockIP(ip: string): void {
    this.BLOCKED_IPS.add(ip)
  }
  
  static unblockIP(ip: string): void {
    this.BLOCKED_IPS.delete(ip)
  }
  
  static isSuspicious(ip: string): boolean {
    return this.SUSPICIOUS_PATTERNS.some(pattern => pattern.test(ip))
  }
  
  static validateIP(ip: string): boolean {
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/
    
    return ipv4Regex.test(ip) || ipv6Regex.test(ip)
  }
}

// 输入验证中间件
export function validateInput<T>(schema: z.ZodSchema<T>) {
  return (data: unknown): { success: true; data: T } | { success: false; errors: string[] } => {
    try {
      const validatedData = schema.parse(data)
      return { success: true, data: validatedData }
    } catch (error) {
      if (error instanceof z.ZodError) {
        const errors = error.errors.map(err => err.message)
        return { success: false, errors }
      }
      return { success: false, errors: ['验证失败'] }
    }
  }
}

// 安全日志记录
export function logSecurityEvent(
  event: string,
  details: Record<string, any>,
  severity: 'low' | 'medium' | 'high' | 'critical' = 'medium'
): void {
  const logEntry = {
    timestamp: new Date().toISOString(),
    event,
    severity,
    details,
    userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
    ip: details.ip || 'unknown',
  }
  
  // 发送到安全监控系统
  console.warn('Security Event:', logEntry)
  
  // 在生产环境中，这里应该发送到专门的安全监控服务
  if (process.env.NODE_ENV === 'production') {
    // 发送到安全监控API
    fetch('/api/security/events', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(logEntry),
    }).catch(console.error)
  }
}
