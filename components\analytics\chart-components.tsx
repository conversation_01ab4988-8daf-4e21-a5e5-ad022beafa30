/**
 * 图表组件
 * 使用原生SVG实现的简单图表组件
 */

'use client'

import { useMemo } from 'react'
import type { ChartData, TimeSeriesDataPoint } from '@/types/analytics'

interface BaseChartProps {
  width?: number
  height?: number
  className?: string
}

// 线性图表组件
interface LineChartProps extends BaseChartProps {
  data: TimeSeriesDataPoint[]
  color?: string
  showGrid?: boolean
  showDots?: boolean
}

export function LineChart({ 
  data, 
  width = 400, 
  height = 200, 
  color = '#3b82f6',
  showGrid = true,
  showDots = false,
  className = ''
}: LineChartProps) {
  const { points, maxValue, minValue } = useMemo(() => {
    if (!data.length) return { points: '', maxValue: 0, minValue: 0 }

    const values = data.map(d => d.value)
    const max = Math.max(...values)
    const min = Math.min(...values)
    const range = max - min || 1

    const stepX = width / (data.length - 1 || 1)
    const points = data.map((point, index) => {
      const x = index * stepX
      const y = height - ((point.value - min) / range) * height
      return `${x},${y}`
    }).join(' ')

    return { points, maxValue: max, minValue: min }
  }, [data, width, height])

  return (
    <div className={`relative ${className}`}>
      <svg width={width} height={height} className="overflow-visible">
        {/* 网格线 */}
        {showGrid && (
          <g className="opacity-20">
            {/* 水平网格线 */}
            {[0, 0.25, 0.5, 0.75, 1].map((ratio) => (
              <line
                key={`h-${ratio}`}
                x1={0}
                y1={height * ratio}
                x2={width}
                y2={height * ratio}
                stroke="currentColor"
                strokeWidth={1}
              />
            ))}
            {/* 垂直网格线 */}
            {data.map((_, index) => (
              <line
                key={`v-${index}`}
                x1={(index * width) / (data.length - 1 || 1)}
                y1={0}
                x2={(index * width) / (data.length - 1 || 1)}
                y2={height}
                stroke="currentColor"
                strokeWidth={1}
              />
            ))}
          </g>
        )}

        {/* 线条 */}
        {points && (
          <polyline
            points={points}
            fill="none"
            stroke={color}
            strokeWidth={2}
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        )}

        {/* 数据点 */}
        {showDots && data.map((point, index) => {
          const x = (index * width) / (data.length - 1 || 1)
          const y = height - ((point.value - minValue) / (maxValue - minValue || 1)) * height
          return (
            <circle
              key={index}
              cx={x}
              cy={y}
              r={3}
              fill={color}
              className="hover:r-4 transition-all"
            />
          )
        })}
      </svg>

      {/* Y轴标签 */}
      <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-muted-foreground -ml-12">
        <span>{maxValue.toLocaleString()}</span>
        <span>{((maxValue + minValue) / 2).toLocaleString()}</span>
        <span>{minValue.toLocaleString()}</span>
      </div>
    </div>
  )
}

// 柱状图组件
interface BarChartProps extends BaseChartProps {
  data: Array<{ label: string; value: number; color?: string }>
  showValues?: boolean
}

export function BarChart({ 
  data, 
  width = 400, 
  height = 200, 
  showValues = true,
  className = ''
}: BarChartProps) {
  const { maxValue, barWidth } = useMemo(() => {
    const max = Math.max(...data.map(d => d.value))
    const barWidth = (width - (data.length - 1) * 8) / data.length
    return { maxValue: max, barWidth }
  }, [data, width])

  return (
    <div className={`relative ${className}`}>
      <svg width={width} height={height + 40} className="overflow-visible">
        {data.map((item, index) => {
          const barHeight = (item.value / maxValue) * height
          const x = index * (barWidth + 8)
          const y = height - barHeight

          return (
            <g key={index}>
              {/* 柱子 */}
              <rect
                x={x}
                y={y}
                width={barWidth}
                height={barHeight}
                fill={item.color || '#3b82f6'}
                className="hover:opacity-80 transition-opacity"
                rx={2}
              />
              
              {/* 数值标签 */}
              {showValues && (
                <text
                  x={x + barWidth / 2}
                  y={y - 5}
                  textAnchor="middle"
                  className="text-xs fill-current"
                >
                  {item.value.toLocaleString()}
                </text>
              )}

              {/* X轴标签 */}
              <text
                x={x + barWidth / 2}
                y={height + 20}
                textAnchor="middle"
                className="text-xs fill-muted-foreground"
              >
                {item.label}
              </text>
            </g>
          )
        })}
      </svg>
    </div>
  )
}

// 饼图组件
interface PieChartProps extends BaseChartProps {
  data: Array<{ label: string; value: number; color?: string }>
  showLabels?: boolean
  showPercentages?: boolean
}

export function PieChart({ 
  data, 
  width = 200, 
  height = 200, 
  showLabels = true,
  showPercentages = true,
  className = ''
}: PieChartProps) {
  const radius = Math.min(width, height) / 2 - 20
  const centerX = width / 2
  const centerY = height / 2

  const total = data.reduce((sum, item) => sum + item.value, 0)
  
  const colors = [
    '#3b82f6', '#ef4444', '#10b981', '#f59e0b', 
    '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16'
  ]

  let currentAngle = 0

  return (
    <div className={`relative ${className}`}>
      <svg width={width} height={height} className="overflow-visible">
        {data.map((item, index) => {
          const percentage = (item.value / total) * 100
          const angle = (item.value / total) * 360
          const startAngle = currentAngle
          const endAngle = currentAngle + angle
          
          currentAngle += angle

          // 计算路径
          const startAngleRad = (startAngle * Math.PI) / 180
          const endAngleRad = (endAngle * Math.PI) / 180
          
          const x1 = centerX + radius * Math.cos(startAngleRad)
          const y1 = centerY + radius * Math.sin(startAngleRad)
          const x2 = centerX + radius * Math.cos(endAngleRad)
          const y2 = centerY + radius * Math.sin(endAngleRad)
          
          const largeArcFlag = angle > 180 ? 1 : 0
          
          const pathData = [
            `M ${centerX} ${centerY}`,
            `L ${x1} ${y1}`,
            `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
            'Z'
          ].join(' ')

          // 标签位置
          const labelAngle = (startAngle + angle / 2) * Math.PI / 180
          const labelRadius = radius + 30
          const labelX = centerX + labelRadius * Math.cos(labelAngle)
          const labelY = centerY + labelRadius * Math.sin(labelAngle)

          return (
            <g key={index}>
              {/* 扇形 */}
              <path
                d={pathData}
                fill={item.color || colors[index % colors.length]}
                className="hover:opacity-80 transition-opacity"
              />
              
              {/* 标签 */}
              {showLabels && (
                <text
                  x={labelX}
                  y={labelY}
                  textAnchor="middle"
                  className="text-xs fill-current"
                >
                  {item.label}
                  {showPercentages && ` (${percentage.toFixed(1)}%)`}
                </text>
              )}
            </g>
          )
        })}
      </svg>
    </div>
  )
}

// 面积图组件
interface AreaChartProps extends BaseChartProps {
  data: TimeSeriesDataPoint[]
  color?: string
  fillOpacity?: number
}

export function AreaChart({ 
  data, 
  width = 400, 
  height = 200, 
  color = '#3b82f6',
  fillOpacity = 0.3,
  className = ''
}: AreaChartProps) {
  const { pathData, maxValue, minValue } = useMemo(() => {
    if (!data.length) return { pathData: '', maxValue: 0, minValue: 0 }

    const values = data.map(d => d.value)
    const max = Math.max(...values)
    const min = Math.min(...values)
    const range = max - min || 1

    const stepX = width / (data.length - 1 || 1)
    
    // 创建路径
    let pathData = `M 0 ${height}`
    
    data.forEach((point, index) => {
      const x = index * stepX
      const y = height - ((point.value - min) / range) * height
      if (index === 0) {
        pathData += ` L ${x} ${y}`
      } else {
        pathData += ` L ${x} ${y}`
      }
    })
    
    pathData += ` L ${width} ${height} Z`

    return { pathData, maxValue: max, minValue: min }
  }, [data, width, height])

  return (
    <div className={`relative ${className}`}>
      <svg width={width} height={height} className="overflow-visible">
        {/* 填充区域 */}
        <path
          d={pathData}
          fill={color}
          fillOpacity={fillOpacity}
        />
        
        {/* 边界线 */}
        <polyline
          points={data.map((point, index) => {
            const x = (index * width) / (data.length - 1 || 1)
            const y = height - ((point.value - minValue) / (maxValue - minValue || 1)) * height
            return `${x},${y}`
          }).join(' ')}
          fill="none"
          stroke={color}
          strokeWidth={2}
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  )
}

// 仪表盘组件
interface GaugeChartProps extends BaseChartProps {
  value: number
  max: number
  min?: number
  color?: string
  label?: string
}

export function GaugeChart({ 
  value, 
  max, 
  min = 0, 
  width = 200, 
  height = 120,
  color = '#3b82f6',
  label = '',
  className = ''
}: GaugeChartProps) {
  const radius = Math.min(width, height * 2) / 2 - 20
  const centerX = width / 2
  const centerY = height - 10
  
  const percentage = Math.min(Math.max((value - min) / (max - min), 0), 1)
  const angle = percentage * 180 - 90 // -90 to 90 degrees
  
  const needleX = centerX + (radius - 20) * Math.cos((angle * Math.PI) / 180)
  const needleY = centerY + (radius - 20) * Math.sin((angle * Math.PI) / 180)

  return (
    <div className={`relative ${className}`}>
      <svg width={width} height={height} className="overflow-visible">
        {/* 背景弧 */}
        <path
          d={`M ${centerX - radius} ${centerY} A ${radius} ${radius} 0 0 1 ${centerX + radius} ${centerY}`}
          fill="none"
          stroke="#e5e7eb"
          strokeWidth={8}
          strokeLinecap="round"
        />
        
        {/* 进度弧 */}
        <path
          d={`M ${centerX - radius} ${centerY} A ${radius} ${radius} 0 0 1 ${needleX} ${needleY}`}
          fill="none"
          stroke={color}
          strokeWidth={8}
          strokeLinecap="round"
        />
        
        {/* 中心点 */}
        <circle
          cx={centerX}
          cy={centerY}
          r={6}
          fill={color}
        />
        
        {/* 指针 */}
        <line
          x1={centerX}
          y1={centerY}
          x2={needleX}
          y2={needleY}
          stroke={color}
          strokeWidth={3}
          strokeLinecap="round"
        />
        
        {/* 数值标签 */}
        <text
          x={centerX}
          y={centerY + 25}
          textAnchor="middle"
          className="text-lg font-bold fill-current"
        >
          {value.toLocaleString()}
        </text>
        
        {/* 标签 */}
        {label && (
          <text
            x={centerX}
            y={centerY + 45}
            textAnchor="middle"
            className="text-sm fill-muted-foreground"
          >
            {label}
          </text>
        )}
        
        {/* 最小值标签 */}
        <text
          x={centerX - radius}
          y={centerY + 20}
          textAnchor="middle"
          className="text-xs fill-muted-foreground"
        >
          {min}
        </text>
        
        {/* 最大值标签 */}
        <text
          x={centerX + radius}
          y={centerY + 20}
          textAnchor="middle"
          className="text-xs fill-muted-foreground"
        >
          {max}
        </text>
      </svg>
    </div>
  )
}
