/**
 * 移动端导航组件
 * 专为移动设备优化的导航界面
 */

'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { 
  Home, 
  ShoppingCart, 
  CreditCard, 
  Package, 
  BarChart3, 
  Settings, 
  Menu, 
  X,
  User,
  Bell,
  Search
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON>et, SheetContent, SheetTrigger, SheetHeader, SheetTitle } from '@/components/ui/sheet'
import { useAuth } from '@/lib/auth/context'

interface MobileNavItem {
  href: string
  label: string
  icon: React.ReactNode
  badge?: number
  description?: string
}

const navigationItems: MobileNavItem[] = [
  {
    href: '/dashboard',
    label: '仪表板',
    icon: <Home className="h-5 w-5" />,
    description: '业务概览'
  },
  {
    href: '/orders',
    label: '订单管理',
    icon: <ShoppingCart className="h-5 w-5" />,
    badge: 3,
    description: '管理所有订单'
  },
  {
    href: '/payments',
    label: '支付管理',
    icon: <CreditCard className="h-5 w-5" />,
    description: '处理支付事务'
  },
  {
    href: '/products',
    label: '商品管理',
    icon: <Package className="h-5 w-5" />,
    description: '管理商品库存'
  },
  {
    href: '/analytics',
    label: '数据统计',
    icon: <BarChart3 className="h-5 w-5" />,
    description: '查看业务数据'
  },
  {
    href: '/settings',
    label: '设置',
    icon: <Settings className="h-5 w-5" />,
    description: '系统设置'
  },
]

export function MobileNav() {
  const pathname = usePathname()
  const { user } = useAuth()
  const [isOpen, setIsOpen] = useState(false)
  const [notifications, setNotifications] = useState(5)

  // 检测是否为移动设备
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // 如果不是移动设备，不显示移动端导航
  if (!isMobile) {
    return null
  }

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === '/' || pathname === '/dashboard'
    }
    return pathname.startsWith(href)
  }

  return (
    <>
      {/* 顶部导航栏 */}
      <div className="fixed top-0 left-0 right-0 z-50 bg-background border-b border-border">
        <div className="flex items-center justify-between px-4 py-3">
          {/* 左侧：菜单按钮和标题 */}
          <div className="flex items-center space-x-3">
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="ghost" size="sm" className="p-2">
                  <Menu className="h-5 w-5" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-80 p-0">
                <div className="flex flex-col h-full">
                  {/* 头部 */}
                  <SheetHeader className="p-6 border-b">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                        <span className="text-white font-bold">柒</span>
                      </div>
                      <div>
                        <SheetTitle className="text-left">柒零连锁</SheetTitle>
                        <p className="text-sm text-muted-foreground">
                          {user?.merchant?.name || '商家管理平台'}
                        </p>
                      </div>
                    </div>
                  </SheetHeader>

                  {/* 导航菜单 */}
                  <div className="flex-1 py-4">
                    <nav className="space-y-1 px-3">
                      {navigationItems.map((item) => (
                        <Link
                          key={item.href}
                          href={item.href}
                          onClick={() => setIsOpen(false)}
                          className={`flex items-center space-x-3 px-3 py-3 rounded-lg transition-colors ${
                            isActive(item.href)
                              ? 'bg-primary text-primary-foreground'
                              : 'hover:bg-muted'
                          }`}
                        >
                          <div className="flex-shrink-0">
                            {item.icon}
                          </div>
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{item.label}</span>
                              {item.badge && (
                                <Badge variant="secondary" className="ml-2">
                                  {item.badge}
                                </Badge>
                              )}
                            </div>
                            {item.description && (
                              <p className="text-xs text-muted-foreground mt-1">
                                {item.description}
                              </p>
                            )}
                          </div>
                        </Link>
                      ))}
                    </nav>
                  </div>

                  {/* 底部用户信息 */}
                  <div className="border-t p-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                        <User className="h-4 w-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">
                          {user?.name || '用户'}
                        </p>
                        <p className="text-xs text-muted-foreground truncate">
                          {user?.email || ''}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </SheetContent>
            </Sheet>

            <h1 className="text-lg font-semibold truncate">
              {navigationItems.find(item => isActive(item.href))?.label || '柒零连锁'}
            </h1>
          </div>

          {/* 右侧：搜索和通知 */}
          <div className="flex items-center space-x-2">
            <Button variant="ghost" size="sm" className="p-2">
              <Search className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="sm" className="p-2 relative">
              <Bell className="h-5 w-5" />
              {notifications > 0 && (
                <Badge 
                  variant="destructive" 
                  className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
                >
                  {notifications > 9 ? '9+' : notifications}
                </Badge>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* 底部导航栏 */}
      <div className="fixed bottom-0 left-0 right-0 z-50 bg-background border-t border-border">
        <div className="grid grid-cols-5 gap-1 px-2 py-2">
          {navigationItems.slice(0, 5).map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`flex flex-col items-center justify-center py-2 px-1 rounded-lg transition-colors relative ${
                isActive(item.href)
                  ? 'text-primary bg-primary/10'
                  : 'text-muted-foreground hover:text-foreground hover:bg-muted'
              }`}
            >
              <div className="relative">
                {item.icon}
                {item.badge && (
                  <Badge 
                    variant="destructive" 
                    className="absolute -top-2 -right-2 h-4 w-4 p-0 flex items-center justify-center text-xs"
                  >
                    {item.badge > 9 ? '9+' : item.badge}
                  </Badge>
                )}
              </div>
              <span className="text-xs mt-1 font-medium">{item.label}</span>
            </Link>
          ))}
        </div>
      </div>

      {/* 为固定导航栏预留空间 */}
      <div className="h-16" /> {/* 顶部空间 */}
      <div className="h-20" /> {/* 底部空间 */}
    </>
  )
}

// 移动端页面容器
interface MobilePageContainerProps {
  children: React.ReactNode
  className?: string
}

export function MobilePageContainer({ children, className = '' }: MobilePageContainerProps) {
  return (
    <div className={`min-h-screen bg-background ${className}`}>
      <MobileNav />
      <main className="pb-20 pt-16 px-4">
        {children}
      </main>
    </div>
  )
}

// 移动端卡片组件
interface MobileCardProps {
  title: string
  description?: string
  children: React.ReactNode
  className?: string
  action?: React.ReactNode
}

export function MobileCard({ title, description, children, className = '', action }: MobileCardProps) {
  return (
    <div className={`bg-card border border-border rounded-lg p-4 ${className}`}>
      <div className="flex items-center justify-between mb-3">
        <div>
          <h3 className="font-semibold">{title}</h3>
          {description && (
            <p className="text-sm text-muted-foreground">{description}</p>
          )}
        </div>
        {action}
      </div>
      {children}
    </div>
  )
}

// 移动端列表项组件
interface MobileListItemProps {
  title: string
  subtitle?: string
  description?: string
  icon?: React.ReactNode
  badge?: React.ReactNode
  action?: React.ReactNode
  onClick?: () => void
  className?: string
}

export function MobileListItem({ 
  title, 
  subtitle, 
  description, 
  icon, 
  badge, 
  action, 
  onClick,
  className = ''
}: MobileListItemProps) {
  return (
    <div 
      className={`flex items-center space-x-3 p-3 rounded-lg transition-colors ${
        onClick ? 'cursor-pointer hover:bg-muted active:bg-muted/80' : ''
      } ${className}`}
      onClick={onClick}
    >
      {icon && (
        <div className="flex-shrink-0">
          {icon}
        </div>
      )}
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center justify-between">
          <h4 className="font-medium truncate">{title}</h4>
          {badge}
        </div>
        {subtitle && (
          <p className="text-sm text-muted-foreground truncate">{subtitle}</p>
        )}
        {description && (
          <p className="text-xs text-muted-foreground mt-1">{description}</p>
        )}
      </div>
      
      {action && (
        <div className="flex-shrink-0">
          {action}
        </div>
      )}
    </div>
  )
}
