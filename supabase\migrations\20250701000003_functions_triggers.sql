-- 柒零支付连锁平台数据库函数和触发器
-- 创建时间: 2025-07-01
-- 描述: 创建有用的数据库函数和自动化触发器

-- 创建更新时间戳的函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有表添加更新时间戳触发器
CREATE TRIGGER update_merchants_updated_at BEFORE UPDATE ON merchants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_hotels_updated_at BEFORE UPDATE ON hotels
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_room_types_updated_at BEFORE UPDATE ON room_types
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_restaurants_updated_at BEFORE UPDATE ON restaurants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_menu_categories_updated_at BEFORE UPDATE ON menu_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_menu_items_updated_at BEFORE UPDATE ON menu_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_product_categories_updated_at BEFORE UPDATE ON product_categories
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 生成订单号的函数
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    order_number TEXT;
    current_date_str TEXT;
    sequence_num INTEGER;
BEGIN
    -- 获取当前日期字符串 (YYYYMMDD)
    current_date_str := TO_CHAR(NOW(), 'YYYYMMDD');
    
    -- 获取当天的订单序号
    SELECT COALESCE(MAX(CAST(SUBSTRING(order_number FROM 9) AS INTEGER)), 0) + 1
    INTO sequence_num
    FROM orders
    WHERE order_number LIKE current_date_str || '%';
    
    -- 生成订单号: YYYYMMDD + 4位序号
    order_number := current_date_str || LPAD(sequence_num::TEXT, 4, '0');
    
    RETURN order_number;
END;
$$ LANGUAGE plpgsql;

-- 自动生成订单号的触发器函数
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为订单表添加自动生成订单号的触发器
CREATE TRIGGER set_order_number_trigger BEFORE INSERT ON orders
    FOR EACH ROW EXECUTE FUNCTION set_order_number();

-- 更新酒店可用房间数的函数
CREATE OR REPLACE FUNCTION update_hotel_available_rooms()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新酒店的总房间数和可用房间数
    UPDATE hotels 
    SET 
        total_rooms = (
            SELECT COALESCE(SUM(total_rooms), 0) 
            FROM room_types 
            WHERE hotel_id = COALESCE(NEW.hotel_id, OLD.hotel_id)
        ),
        available_rooms = (
            SELECT COALESCE(SUM(available_rooms), 0) 
            FROM room_types 
            WHERE hotel_id = COALESCE(NEW.hotel_id, OLD.hotel_id)
        )
    WHERE id = COALESCE(NEW.hotel_id, OLD.hotel_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 为房间类型表添加更新酒店房间数的触发器
CREATE TRIGGER update_hotel_rooms_on_room_type_change
    AFTER INSERT OR UPDATE OR DELETE ON room_types
    FOR EACH ROW EXECUTE FUNCTION update_hotel_available_rooms();

-- 计算订单总金额的函数
CREATE OR REPLACE FUNCTION calculate_order_total(
    p_subtotal DECIMAL(10,2),
    p_tax_amount DECIMAL(10,2) DEFAULT 0,
    p_discount_amount DECIMAL(10,2) DEFAULT 0
)
RETURNS DECIMAL(10,2) AS $$
BEGIN
    RETURN p_subtotal + COALESCE(p_tax_amount, 0) - COALESCE(p_discount_amount, 0);
END;
$$ LANGUAGE plpgsql;

-- 自动计算订单总金额的触发器函数
CREATE OR REPLACE FUNCTION set_order_total()
RETURNS TRIGGER AS $$
BEGIN
    NEW.total_amount := calculate_order_total(
        NEW.subtotal, 
        NEW.tax_amount, 
        NEW.discount_amount
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为订单表添加自动计算总金额的触发器
CREATE TRIGGER set_order_total_trigger BEFORE INSERT OR UPDATE ON orders
    FOR EACH ROW EXECUTE FUNCTION set_order_total();

-- 获取商家统计数据的函数
CREATE OR REPLACE FUNCTION get_merchant_stats(merchant_uuid UUID)
RETURNS JSON AS $$
DECLARE
    stats JSON;
BEGIN
    SELECT json_build_object(
        'total_orders', (
            SELECT COUNT(*) FROM orders WHERE merchant_id = merchant_uuid
        ),
        'pending_orders', (
            SELECT COUNT(*) FROM orders 
            WHERE merchant_id = merchant_uuid AND status = 'pending'
        ),
        'completed_orders', (
            SELECT COUNT(*) FROM orders 
            WHERE merchant_id = merchant_uuid AND status = 'completed'
        ),
        'total_revenue', (
            SELECT COALESCE(SUM(total_amount), 0) FROM orders 
            WHERE merchant_id = merchant_uuid AND status = 'completed'
        ),
        'total_hotels', (
            SELECT COUNT(*) FROM hotels WHERE merchant_id = merchant_uuid
        ),
        'total_restaurants', (
            SELECT COUNT(*) FROM restaurants WHERE merchant_id = merchant_uuid
        ),
        'total_products', (
            SELECT COUNT(*) FROM products WHERE merchant_id = merchant_uuid
        ),
        'low_stock_products', (
            SELECT COUNT(*) FROM products 
            WHERE merchant_id = merchant_uuid 
            AND stock_quantity <= min_stock_level
        )
    ) INTO stats;
    
    RETURN stats;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 搜索功能函数
CREATE OR REPLACE FUNCTION search_products(
    merchant_uuid UUID,
    search_query TEXT DEFAULT '',
    category_uuid UUID DEFAULT NULL,
    limit_count INTEGER DEFAULT 20,
    offset_count INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name VARCHAR(255),
    description TEXT,
    price DECIMAL(10,2),
    stock_quantity INTEGER,
    images TEXT[],
    category_name VARCHAR(255)
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.description,
        p.price,
        p.stock_quantity,
        p.images,
        pc.name as category_name
    FROM products p
    LEFT JOIN product_categories pc ON p.category_id = pc.id
    WHERE p.merchant_id = merchant_uuid
    AND p.is_active = true
    AND (search_query = '' OR p.name ILIKE '%' || search_query || '%' 
         OR p.description ILIKE '%' || search_query || '%')
    AND (category_uuid IS NULL OR p.category_id = category_uuid)
    ORDER BY p.name
    LIMIT limit_count OFFSET offset_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 创建全文搜索索引
CREATE INDEX idx_products_search ON products USING gin(to_tsvector('chinese', name || ' ' || COALESCE(description, '')));
CREATE INDEX idx_menu_items_search ON menu_items USING gin(to_tsvector('chinese', name || ' ' || COALESCE(description, '')));
CREATE INDEX idx_hotels_search ON hotels USING gin(to_tsvector('chinese', name || ' ' || COALESCE(description, '')));
CREATE INDEX idx_restaurants_search ON restaurants USING gin(to_tsvector('chinese', name || ' ' || COALESCE(description, '')));
