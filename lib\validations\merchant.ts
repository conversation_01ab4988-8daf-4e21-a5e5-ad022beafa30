/**
 * 商家信息验证模式
 * 使用 Zod 进行商家数据验证
 */

import { z } from 'zod'

// 营业时间验证模式
const businessHoursSchema = z.object({
  monday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    isOpen: z.boolean(),
  }),
  tuesday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    isOpen: z.boolean(),
  }),
  wednesday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    isOpen: z.boolean(),
  }),
  thursday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    isOpen: z.boolean(),
  }),
  friday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    isOpen: z.boolean(),
  }),
  saturday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    isOpen: z.boolean(),
  }),
  sunday: z.object({
    open: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    close: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, '请输入有效的时间格式 (HH:MM)'),
    isOpen: z.boolean(),
  }),
})

// 联系信息验证模式
const contactInfoSchema = z.object({
  phone: z
    .string()
    .optional()
    .refine((val) => !val || /^1[3-9]\d{9}$/.test(val), {
      message: '请输入有效的手机号码',
    }),
  email: z
    .string()
    .optional()
    .refine((val) => !val || z.string().email().safeParse(val).success, {
      message: '请输入有效的邮箱地址',
    }),
  address: z
    .string()
    .optional()
    .refine((val) => !val || val.length >= 5, {
      message: '地址至少需要5个字符',
    }),
  website: z
    .string()
    .optional()
    .refine((val) => !val || z.string().url().safeParse(val).success, {
      message: '请输入有效的网站地址',
    }),
})

// 商家信息更新验证模式
export const merchantUpdateSchema = z.object({
  name: z
    .string()
    .min(1, '请输入商家名称')
    .min(2, '商家名称至少需要2个字符')
    .max(100, '商家名称不能超过100个字符'),
  description: z
    .string()
    .optional()
    .refine((val) => !val || val.length <= 500, {
      message: '商家描述不能超过500个字符',
    }),
  business_types: z
    .array(z.enum(['hotel', 'restaurant', 'product']))
    .min(1, '请至少选择一种业务类型'),
  contact_info: contactInfoSchema,
  business_hours: businessHoursSchema.optional(),
  logo: z
    .string()
    .optional()
    .refine((val) => !val || z.string().url().safeParse(val).success, {
      message: '请输入有效的Logo URL',
    }),
})

// 商家状态更新验证模式
export const merchantStatusSchema = z.object({
  status: z.enum(['active', 'inactive', 'suspended'], {
    required_error: '请选择商家状态',
  }),
})

// 图片上传验证模式
export const imageUploadSchema = z.object({
  file: z
    .instanceof(File)
    .refine((file) => file.size <= 5 * 1024 * 1024, '图片大小不能超过5MB')
    .refine(
      (file) => ['image/jpeg', 'image/png', 'image/webp'].includes(file.type),
      '只支持 JPEG、PNG、WebP 格式的图片'
    ),
})

// 类型导出
export type MerchantUpdateFormData = z.infer<typeof merchantUpdateSchema>
export type MerchantStatusFormData = z.infer<typeof merchantStatusSchema>
export type ImageUploadFormData = z.infer<typeof imageUploadSchema>
export type BusinessHours = z.infer<typeof businessHoursSchema>
export type ContactInfo = z.infer<typeof contactInfoSchema>

// 默认营业时间
export const defaultBusinessHours: BusinessHours = {
  monday: { open: '09:00', close: '18:00', isOpen: true },
  tuesday: { open: '09:00', close: '18:00', isOpen: true },
  wednesday: { open: '09:00', close: '18:00', isOpen: true },
  thursday: { open: '09:00', close: '18:00', isOpen: true },
  friday: { open: '09:00', close: '18:00', isOpen: true },
  saturday: { open: '09:00', close: '18:00', isOpen: true },
  sunday: { open: '09:00', close: '18:00', isOpen: false },
}
