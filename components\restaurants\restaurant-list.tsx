/**
 * 餐厅列表组件
 * 显示餐厅列表，支持搜索和筛选
 */

'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { MoreHorizontal, Edit, Trash2, Eye, MapPin, Phone, Star, Clock } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { deleteRestaurant } from '@/lib/actions/restaurants'
import { 
  RESTAURANT_TYPE_CONFIG, 
  AMENITY_CONFIG,
  type RestaurantWithDetails
} from '@/types/restaurant'

interface RestaurantListProps {
  restaurants: RestaurantWithDetails[]
  onRestaurantDeleted?: () => void
}

export function RestaurantList({ restaurants, onRestaurantDeleted }: RestaurantListProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [isDeleting, setIsDeleting] = useState<string | null>(null)

  // 过滤餐厅
  const filteredRestaurants = restaurants.filter(restaurant =>
    restaurant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    restaurant.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
    restaurant.address?.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const handleDelete = async (restaurantId: string, restaurantName: string) => {
    if (!confirm(`确定要删除餐厅"${restaurantName}"吗？此操作不可撤销。`)) {
      return
    }

    setIsDeleting(restaurantId)

    try {
      const result = await deleteRestaurant(restaurantId)

      if (result.success) {
        onRestaurantDeleted?.()
      } else {
        alert(result.error || '删除失败')
      }
    } catch (error) {
      alert('删除失败，请稍后重试')
    } finally {
      setIsDeleting(null)
    }
  }

  const formatBusinessHours = (businessHours: any) => {
    if (!businessHours || Object.keys(businessHours).length === 0) {
      return '营业时间未设置'
    }

    const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase()
    const todayHours = businessHours[today]

    if (!todayHours || !todayHours.isOpen) {
      return '今日休息'
    }

    return `今日 ${todayHours.openTime} - ${todayHours.closeTime}`
  }

  return (
    <div className="space-y-6">
      {/* 搜索栏 */}
      <div className="flex items-center space-x-4">
        <div className="flex-1">
          <Input
            placeholder="搜索餐厅名称、描述或地址..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Link href="/restaurants/new">
          <Button>添加餐厅</Button>
        </Link>
      </div>

      {/* 餐厅列表 */}
      {filteredRestaurants.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                {searchQuery ? '没有找到匹配的餐厅' : '还没有添加任何餐厅'}
              </p>
              {!searchQuery && (
                <Link href="/restaurants/new">
                  <Button>添加第一个餐厅</Button>
                </Link>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredRestaurants.map((restaurant) => (
            <Card key={restaurant.id} className="overflow-hidden">
              {/* 餐厅图片 */}
              <div className="relative h-48 bg-muted">
                {restaurant.images && restaurant.images.length > 0 ? (
                  <Image
                    src={restaurant.images[0]}
                    alt={restaurant.name}
                    fill
                    className="object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <span className="text-4xl">🍽️</span>
                  </div>
                )}
                
                {/* 状态标识 */}
                <div className="absolute top-2 right-2">
                  <Badge variant={restaurant.is_active ? 'success' : 'secondary'}>
                    {restaurant.is_active ? '营业中' : '暂停营业'}
                  </Badge>
                </div>
              </div>

              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">{restaurant.name}</CardTitle>
                    {restaurant.address && (
                      <div className="flex items-center text-sm text-muted-foreground mt-1">
                        <MapPin className="h-3 w-3 mr-1" />
                        {restaurant.address}
                      </div>
                    )}
                  </div>
                  
                  {/* 评分 */}
                  {restaurant.rating && restaurant.rating > 0 && (
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">{restaurant.rating}</span>
                    </div>
                  )}
                </div>
              </CardHeader>

              <CardContent className="space-y-4">
                {/* 餐厅描述 */}
                {restaurant.description && (
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {restaurant.description}
                  </p>
                )}

                {/* 餐厅类型 */}
                {restaurant.restaurant_type && restaurant.restaurant_type.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {restaurant.restaurant_type.slice(0, 3).map((type) => (
                      <Badge key={type} variant="outline" className="text-xs">
                        {RESTAURANT_TYPE_CONFIG[type as keyof typeof RESTAURANT_TYPE_CONFIG]?.icon}{' '}
                        {RESTAURANT_TYPE_CONFIG[type as keyof typeof RESTAURANT_TYPE_CONFIG]?.label || type}
                      </Badge>
                    ))}
                    {restaurant.restaurant_type.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{restaurant.restaurant_type.length - 3}
                      </Badge>
                    )}
                  </div>
                )}

                {/* 联系信息和营业时间 */}
                <div className="space-y-1">
                  {restaurant.phone && (
                    <div className="flex items-center text-sm text-muted-foreground">
                      <Phone className="h-3 w-3 mr-2" />
                      {restaurant.phone}
                    </div>
                  )}
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Clock className="h-3 w-3 mr-2" />
                    {formatBusinessHours(restaurant.business_hours)}
                  </div>
                </div>

                {/* 设施 */}
                {restaurant.amenities && restaurant.amenities.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {restaurant.amenities.slice(0, 3).map((amenity) => (
                      <Badge key={amenity} variant="outline" className="text-xs">
                        {AMENITY_CONFIG[amenity as keyof typeof AMENITY_CONFIG]?.icon}{' '}
                        {AMENITY_CONFIG[amenity as keyof typeof AMENITY_CONFIG]?.label || amenity}
                      </Badge>
                    ))}
                    {restaurant.amenities.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{restaurant.amenities.length - 3}
                      </Badge>
                    )}
                  </div>
                )}

                {/* 菜单统计 */}
                <div className="flex items-center justify-between text-sm text-muted-foreground">
                  <span>菜品数: {restaurant.menu_items?.length || 0}</span>
                  <span>分类: {restaurant.menu_categories?.length || 0}</span>
                  {restaurant.average_price && (
                    <span>人均: ¥{restaurant.average_price}</span>
                  )}
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center justify-between pt-2">
                  <div className="flex items-center space-x-2">
                    <Link href={`/restaurants/${restaurant.id}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        查看
                      </Button>
                    </Link>
                    <Link href={`/restaurants/${restaurant.id}/edit`}>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-1" />
                        编辑
                      </Button>
                    </Link>
                  </div>
                  
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(restaurant.id, restaurant.name)}
                    disabled={isDeleting === restaurant.id}
                    className="text-destructive hover:text-destructive"
                  >
                    {isDeleting === restaurant.id ? (
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-destructive" />
                    ) : (
                      <Trash2 className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
