/**
 * 离线页面
 * 当用户离线时显示的页面
 */

'use client'

import { useEffect, useState } from 'react'
import { Wifi, WifiOff, RefreshCw, Home } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'

export default function OfflinePage() {
  const [isOnline, setIsOnline] = useState(true)
  const [retryCount, setRetryCount] = useState(0)

  useEffect(() => {
    // 检查网络状态
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine)
    }

    // 监听网络状态变化
    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)

    // 初始检查
    updateOnlineStatus()

    return () => {
      window.removeEventListener('online', updateOnlineStatus)
      window.removeEventListener('offline', updateOnlineStatus)
    }
  }, [])

  const handleRetry = () => {
    setRetryCount(prev => prev + 1)
    
    // 尝试重新加载页面
    if (navigator.onLine) {
      window.location.reload()
    } else {
      // 如果仍然离线，显示提示
      setTimeout(() => {
        if (!navigator.onLine) {
          alert('网络仍未连接，请检查您的网络设置')
        }
      }, 1000)
    }
  }

  const goHome = () => {
    window.location.href = '/'
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {isOnline ? (
              <Wifi className="h-16 w-16 text-green-500" />
            ) : (
              <WifiOff className="h-16 w-16 text-red-500" />
            )}
          </div>
          <CardTitle className="text-xl">
            {isOnline ? '网络已连接' : '网络连接中断'}
          </CardTitle>
          <CardDescription>
            {isOnline 
              ? '网络连接已恢复，您可以继续使用应用'
              : '请检查您的网络连接并重试'
            }
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* 网络状态信息 */}
          <div className="bg-muted rounded-lg p-4">
            <h4 className="font-medium mb-2">网络状态</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>连接状态:</span>
                <span className={isOnline ? 'text-green-600' : 'text-red-600'}>
                  {isOnline ? '已连接' : '已断开'}
                </span>
              </div>
              <div className="flex justify-between">
                <span>重试次数:</span>
                <span>{retryCount}</span>
              </div>
              <div className="flex justify-between">
                <span>连接类型:</span>
                <span>
                  {(navigator as any).connection?.effectiveType || '未知'}
                </span>
              </div>
            </div>
          </div>

          {/* 离线功能说明 */}
          {!isOnline && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">离线模式</h4>
              <div className="text-sm text-blue-800 space-y-1">
                <p>• 您可以查看已缓存的页面</p>
                <p>• 部分功能可能无法使用</p>
                <p>• 数据将在网络恢复后同步</p>
              </div>
            </div>
          )}

          {/* 故障排除建议 */}
          {!isOnline && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-900 mb-2">故障排除</h4>
              <div className="text-sm text-yellow-800 space-y-1">
                <p>1. 检查WiFi或移动数据连接</p>
                <p>2. 尝试访问其他网站</p>
                <p>3. 重启路由器或移动设备</p>
                <p>4. 联系网络服务提供商</p>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex space-x-2">
            <Button 
              onClick={handleRetry} 
              className="flex-1"
              disabled={!isOnline && retryCount > 3}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              {isOnline ? '重新加载' : '重试连接'}
            </Button>
            
            <Button 
              variant="outline" 
              onClick={goHome}
              className="flex-1"
            >
              <Home className="h-4 w-4 mr-2" />
              返回首页
            </Button>
          </div>

          {/* 自动重试提示 */}
          {!isOnline && retryCount <= 3 && (
            <p className="text-xs text-muted-foreground text-center">
              应用将自动检测网络连接并重试
            </p>
          )}

          {/* 联系支持 */}
          <div className="text-center pt-4 border-t">
            <p className="text-xs text-muted-foreground mb-2">
              如果问题持续存在，请联系技术支持
            </p>
            <div className="flex justify-center space-x-4 text-xs">
              <a 
                href="tel:************" 
                className="text-blue-600 hover:underline"
              >
                📞 ************
              </a>
              <a 
                href="mailto:<EMAIL>" 
                className="text-blue-600 hover:underline"
              >
                ✉️ <EMAIL>
              </a>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 自动重连逻辑 */}
      {!isOnline && (
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // 自动检测网络恢复
              let reconnectInterval;
              
              function checkConnection() {
                if (navigator.onLine) {
                  clearInterval(reconnectInterval);
                  window.location.reload();
                }
              }
              
              // 每5秒检查一次网络状态
              reconnectInterval = setInterval(checkConnection, 5000);
              
              // 监听网络状态变化
              window.addEventListener('online', function() {
                clearInterval(reconnectInterval);
                setTimeout(() => {
                  window.location.reload();
                }, 1000);
              });
            `,
          }}
        />
      )}
    </div>
  )
}
