/**
 * 商品管理页面
 * 显示商品列表和管理功能
 */

'use client'

import { useState, useEffect } from 'react'
import { PageContainer } from '@/components/layouts/page-container'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ProductList } from '@/components/products/product-list'
import { useAuth } from '@/lib/auth/context'
import { getProducts } from '@/lib/actions/products'
import { type ProductWithDetails } from '@/types/product'

export default function ProductsPage() {
  const { user } = useAuth()
  const [products, setProducts] = useState<ProductWithDetails[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')

  useEffect(() => {
    loadProducts()
  }, [])

  const loadProducts = async () => {
    setIsLoading(true)
    setError('')

    try {
      const result = await getProducts()

      if (result.success && Array.isArray(result.data)) {
        setProducts(result.data)
      } else {
        setError(result.error || '获取商品列表失败')
      }
    } catch (err) {
      setError('获取商品列表失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  // 检查用户是否有商品业务权限
  const hasProductBusiness = user?.merchant?.business_types?.includes('product')

  if (!hasProductBusiness) {
    return (
      <PageContainer
        title="商品管理"
        description="管理您的商品信息和库存"
        breadcrumbs={[
          { title: '仪表板', href: '/dashboard' },
          { title: '商品管理' }
        ]}
      >
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="text-4xl mb-4">📦</div>
              <h3 className="text-lg font-semibold mb-2">商品业务未开通</h3>
              <p className="text-muted-foreground mb-4">
                您的商家还未开通商品管理业务，请先在商家资料中添加商品业务类型。
              </p>
              <a
                href="/profile"
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90"
              >
                前往商家资料
              </a>
            </div>
          </CardContent>
        </Card>
      </PageContainer>
    )
  }

  if (isLoading) {
    return (
      <PageContainer
        title="商品管理"
        description="管理您的商品信息和库存"
        breadcrumbs={[
          { title: '仪表板', href: '/dashboard' },
          { title: '商品管理' }
        ]}
      >
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </PageContainer>
    )
  }

  if (error) {
    return (
      <PageContainer
        title="商品管理"
        description="管理您的商品信息和库存"
        breadcrumbs={[
          { title: '仪表板', href: '/dashboard' },
          { title: '商品管理' }
        ]}
      >
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-destructive mb-4">{error}</p>
              <button
                onClick={loadProducts}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90"
              >
                重新加载
              </button>
            </div>
          </CardContent>
        </Card>
      </PageContainer>
    )
  }

  // 计算统计数据
  const stats = {
    total: products.length,
    active: products.filter(p => p.is_active).length,
    inactive: products.filter(p => !p.is_active).length,
    outOfStock: products.filter(p => (p.stock_quantity || 0) <= 0).length,
    lowStock: products.filter(p => {
      const quantity = p.stock_quantity || 0
      return quantity > 0 && quantity <= 10
    }).length,
    featured: products.filter(p => p.is_featured).length,
    totalValue: products.reduce((sum, p) => sum + (p.base_price * (p.stock_quantity || 0)), 0),
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`
  }

  return (
    <PageContainer
      title="商品管理"
      description="管理您的商品信息和库存"
      breadcrumbs={[
        { title: '仪表板', href: '/dashboard' },
        { title: '商品管理' }
      ]}
    >
      <div className="space-y-6">
        {/* 统计卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">总商品数</CardTitle>
              <span className="text-2xl">📦</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                已注册的商品数量
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">在售商品</CardTitle>
              <Badge variant="success" className="text-xs">活跃</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">{stats.active}</div>
              <p className="text-xs text-muted-foreground">
                正在销售的商品
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">库存预警</CardTitle>
              <Badge variant="warning" className="text-xs">⚠️</Badge>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-yellow-600">
                {stats.lowStock + stats.outOfStock}
              </div>
              <p className="text-xs text-muted-foreground">
                缺货({stats.outOfStock}) + 低库存({stats.lowStock})
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">库存价值</CardTitle>
              <span className="text-2xl">💰</span>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                {formatCurrency(stats.totalValue)}
              </div>
              <p className="text-xs text-muted-foreground">
                当前库存总价值
              </p>
            </CardContent>
          </Card>
        </div>

        {/* 快速统计 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">商品状态分布</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">在售商品</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-muted rounded-full h-2">
                      <div 
                        className="h-2 rounded-full bg-green-500" 
                        style={{ width: `${stats.total > 0 ? (stats.active / stats.total) * 100 : 0}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium">{stats.active}</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">下架商品</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-muted rounded-full h-2">
                      <div 
                        className="h-2 rounded-full bg-gray-500" 
                        style={{ width: `${stats.total > 0 ? (stats.inactive / stats.total) * 100 : 0}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium">{stats.inactive}</span>
                  </div>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">推荐商品</span>
                  <div className="flex items-center space-x-2">
                    <div className="w-16 bg-muted rounded-full h-2">
                      <div 
                        className="h-2 rounded-full bg-yellow-500" 
                        style={{ width: `${stats.total > 0 ? (stats.featured / stats.total) * 100 : 0}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium">{stats.featured}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">库存状态</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm">正常库存</span>
                  <span className="text-sm font-medium text-green-600">
                    {stats.total - stats.lowStock - stats.outOfStock}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">库存不足</span>
                  <span className="text-sm font-medium text-yellow-600">{stats.lowStock}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm">缺货</span>
                  <span className="text-sm font-medium text-red-600">{stats.outOfStock}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-base">快速操作</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <a
                  href="/products/new"
                  className="block w-full text-center px-3 py-2 text-sm border border-border rounded-md hover:bg-muted transition-colors"
                >
                  添加新商品
                </a>
                <a
                  href="/products/categories"
                  className="block w-full text-center px-3 py-2 text-sm border border-border rounded-md hover:bg-muted transition-colors"
                >
                  管理分类
                </a>
                <a
                  href="/products/import"
                  className="block w-full text-center px-3 py-2 text-sm border border-border rounded-md hover:bg-muted transition-colors"
                >
                  批量导入
                </a>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 商品列表 */}
        <ProductList products={products} onProductDeleted={loadProducts} />
      </div>
    </PageContainer>
  )
}
