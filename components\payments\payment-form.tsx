/**
 * 支付表单组件
 * 用于创建支付和选择支付方式
 */

'use client'

import { useState, useEffect } from 'react'
import { QRCodeSVG } from 'qrcode.react'
import { CreditCard, Smartphone, Banknote, Clock, CheckCircle, XCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { createPayment, queryPaymentStatus } from '@/lib/actions/payments'
import { paymentGateway } from '@/lib/payments/payment-gateway'
import { 
  PAYMENT_METHOD_CONFIG,
  PAYMENT_STATUS_CONFIG,
  type PaymentMethod,
  type Payment
} from '@/types/payment'

interface PaymentFormProps {
  orderId: string
  amount: number
  description: string
  customerName?: string
  customerPhone?: string
  customerEmail?: string
  onSuccess?: (payment: Payment) => void
  onCancel?: () => void
}

export function PaymentForm({
  orderId,
  amount,
  description,
  customerName,
  customerPhone,
  customerEmail,
  onSuccess,
  onCancel
}: PaymentFormProps) {
  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod | null>(null)
  const [isCreating, setIsCreating] = useState(false)
  const [payment, setPayment] = useState<Payment | null>(null)
  const [paymentStatus, setPaymentStatus] = useState<string>('pending')
  const [error, setError] = useState('')
  const [availableMethods, setAvailableMethods] = useState<PaymentMethod[]>([])

  useEffect(() => {
    // 获取可用的支付方式
    const methods = paymentGateway.getAvailablePaymentMethods()
    setAvailableMethods(methods)
    
    // 默认选择第一个可用方式
    if (methods.length > 0) {
      setSelectedMethod(methods[0])
    }
  }, [])

  useEffect(() => {
    // 如果有支付记录，定期查询状态
    if (payment && ['pending', 'processing'].includes(paymentStatus)) {
      const interval = setInterval(async () => {
        await checkPaymentStatus()
      }, 3000) // 每3秒查询一次

      return () => clearInterval(interval)
    }
  }, [payment, paymentStatus])

  const handleCreatePayment = async () => {
    if (!selectedMethod) {
      setError('请选择支付方式')
      return
    }

    setIsCreating(true)
    setError('')

    try {
      const result = await createPayment(orderId, selectedMethod, {
        amount,
        currency: 'CNY',
        description,
        customerName,
        customerPhone,
        customerEmail,
      })

      if (result.success && result.data) {
        setPayment(result.data as Payment)
        setPaymentStatus(result.data.status)
        
        // 现金支付直接标记为待确认
        if (selectedMethod === 'cash') {
          setPaymentStatus('pending')
        }
      } else {
        setError(result.error || '创建支付失败')
      }
    } catch (err) {
      setError('创建支付失败，请稍后重试')
    } finally {
      setIsCreating(false)
    }
  }

  const checkPaymentStatus = async () => {
    if (!payment) return

    try {
      const result = await queryPaymentStatus(payment.id)
      
      if (result.success && result.data) {
        const newStatus = result.data.status
        setPaymentStatus(newStatus)
        
        if (newStatus === 'completed') {
          onSuccess?.(result.data as Payment)
        }
      }
    } catch (err) {
      console.error('Check payment status error:', err)
    }
  }

  const formatCurrency = (amount: number) => {
    return paymentGateway.formatAmount(amount)
  }

  const getMethodIcon = (method: PaymentMethod) => {
    switch (method) {
      case 'qiling_pay':
        return <CreditCard className="h-5 w-5" />
      case 'alipay':
        return <Smartphone className="h-5 w-5" />
      case 'wechat_pay':
        return <Smartphone className="h-5 w-5" />
      case 'cash':
        return <Banknote className="h-5 w-5" />
      default:
        return <CreditCard className="h-5 w-5" />
    }
  }

  // 如果已创建支付，显示支付状态
  if (payment) {
    const statusConfig = PAYMENT_STATUS_CONFIG[paymentStatus as keyof typeof PAYMENT_STATUS_CONFIG]
    
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center space-x-2">
            <span>{statusConfig?.icon}</span>
            <span>{statusConfig?.label}</span>
          </CardTitle>
          <CardDescription>{statusConfig?.description}</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* 支付信息 */}
          <div className="text-center space-y-2">
            <p className="text-2xl font-bold">{formatCurrency(amount)}</p>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>

          {/* 支付方式 */}
          <div className="flex items-center justify-center space-x-2">
            {getMethodIcon(payment.payment_method as PaymentMethod)}
            <span className="text-sm">
              {PAYMENT_METHOD_CONFIG[payment.payment_method as PaymentMethod]?.label}
            </span>
          </div>

          {/* 二维码支付 */}
          {payment.payment_data?.qr_code && paymentStatus === 'pending' && (
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <QRCodeSVG
                  value={payment.payment_data.qr_code}
                  size={200}
                  className="border rounded-lg p-2"
                />
              </div>
              <p className="text-sm text-muted-foreground">
                {payment.payment_method === 'alipay' ? '请使用支付宝扫描二维码完成支付' :
                 payment.payment_method === 'wechat_pay' ? '请使用微信扫描二维码完成支付' :
                 '请使用手机扫描二维码完成支付'}
              </p>
              {payment.payment_method === 'alipay' && (
                <div className="text-xs text-muted-foreground space-y-1">
                  <p>• 打开支付宝APP</p>
                  <p>• 点击扫一扫</p>
                  <p>• 扫描上方二维码</p>
                </div>
              )}
              {payment.payment_method === 'wechat_pay' && (
                <div className="text-xs text-muted-foreground space-y-1">
                  <p>• 打开微信APP</p>
                  <p>• 点击扫一扫</p>
                  <p>• 扫描上方二维码</p>
                </div>
              )}
            </div>
          )}

          {/* 现金支付说明 */}
          {payment.payment_method === 'cash' && paymentStatus === 'pending' && (
            <div className="text-center space-y-2">
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p className="text-sm text-yellow-800">
                  请准备现金 {formatCurrency(amount)} 并联系工作人员完成支付
                </p>
              </div>
            </div>
          )}

          {/* 支付成功 */}
          {paymentStatus === 'completed' && (
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <CheckCircle className="h-16 w-16 text-green-500" />
              </div>
              <p className="text-green-600 font-medium">支付成功！</p>
            </div>
          )}

          {/* 支付失败 */}
          {paymentStatus === 'failed' && (
            <div className="text-center space-y-4">
              <div className="flex justify-center">
                <XCircle className="h-16 w-16 text-red-500" />
              </div>
              <p className="text-red-600 font-medium">支付失败</p>
              <Button onClick={() => setPayment(null)} variant="outline">
                重新支付
              </Button>
            </div>
          )}

          {/* 处理中状态 */}
          {['pending', 'processing'].includes(paymentStatus) && (
            <div className="text-center">
              <div className="flex items-center justify-center space-x-2 text-blue-600">
                <Clock className="h-4 w-4 animate-spin" />
                <span className="text-sm">等待支付完成...</span>
              </div>
            </div>
          )}

          {/* 操作按钮 */}
          <div className="flex space-x-2">
            {paymentStatus === 'pending' && (
              <Button 
                variant="outline" 
                onClick={() => setPayment(null)}
                className="flex-1"
              >
                重新选择
              </Button>
            )}
            
            {onCancel && (
              <Button 
                variant="outline" 
                onClick={onCancel}
                className="flex-1"
              >
                取消
              </Button>
            )}
            
            {paymentStatus === 'completed' && onSuccess && (
              <Button 
                onClick={() => onSuccess(payment)}
                className="flex-1"
              >
                确认
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  // 显示支付方式选择
  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <CardTitle>选择支付方式</CardTitle>
        <CardDescription>请选择您的支付方式</CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* 错误提示 */}
        {error && (
          <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}

        {/* 支付金额 */}
        <div className="text-center space-y-2">
          <p className="text-2xl font-bold">{formatCurrency(amount)}</p>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>

        {/* 支付方式选择 */}
        <div className="space-y-3">
          <label className="text-sm font-medium">支付方式</label>
          <div className="space-y-2">
            {availableMethods.map((method) => {
              const config = PAYMENT_METHOD_CONFIG[method]
              const isSelected = selectedMethod === method
              
              return (
                <label
                  key={method}
                  className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                    isSelected
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:border-primary/50'
                  }`}
                >
                  <input
                    type="radio"
                    name="paymentMethod"
                    value={method}
                    checked={isSelected}
                    onChange={(e) => setSelectedMethod(e.target.value as PaymentMethod)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border"
                  />
                  <div className="flex items-center space-x-2 flex-1">
                    {getMethodIcon(method)}
                    <div>
                      <p className="font-medium">{config.label}</p>
                      <p className="text-sm text-muted-foreground">{config.description}</p>
                    </div>
                  </div>
                  {config.isEnabled ? (
                    <Badge variant="success" className="text-xs">可用</Badge>
                  ) : (
                    <Badge variant="secondary" className="text-xs">暂停</Badge>
                  )}
                </label>
              )
            })}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2">
          {onCancel && (
            <Button variant="outline" onClick={onCancel} className="flex-1">
              取消
            </Button>
          )}
          <Button 
            onClick={handleCreatePayment}
            disabled={!selectedMethod || isCreating}
            className="flex-1"
          >
            {isCreating ? '创建中...' : '确认支付'}
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
