/**
 * API安全中间件
 * 提供API请求的安全验证和保护
 */

import { NextRequest, NextResponse } from 'next/server'
import { RateLimiter, IPSecurity, CSRFProtection, logSecurityEvent } from '@/lib/security/validation'

// 安全配置
interface SecurityConfig {
  enableRateLimit: boolean
  enableCSRF: boolean
  enableIPBlocking: boolean
  enableCORS: boolean
  maxRequestsPerMinute: number
  allowedOrigins: string[]
  blockedUserAgents: string[]
}

const defaultConfig: SecurityConfig = {
  enableRateLimit: true,
  enableCSRF: true,
  enableIPBlocking: true,
  enableCORS: true,
  maxRequestsPerMinute: 100,
  allowedOrigins: [
    'http://localhost:3000',
    'https://your-domain.com',
  ],
  blockedUserAgents: [
    'bot',
    'crawler',
    'spider',
    'scraper',
  ],
}

// 获取客户端IP地址
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for')
  const realIP = request.headers.get('x-real-ip')
  const remoteAddr = request.headers.get('remote-addr')
  
  if (forwarded) {
    return forwarded.split(',')[0].trim()
  }
  
  return realIP || remoteAddr || 'unknown'
}

// 获取用户标识符
function getUserIdentifier(request: NextRequest): string {
  const ip = getClientIP(request)
  const userAgent = request.headers.get('user-agent') || ''
  const authorization = request.headers.get('authorization') || ''
  
  // 如果有认证信息，使用用户ID
  if (authorization) {
    try {
      // 这里应该解析JWT token获取用户ID
      // 简化处理，使用authorization header的hash
      const crypto = require('crypto')
      const userId = crypto.createHash('md5').update(authorization).digest('hex')
      return `user:${userId}`
    } catch {
      // 降级到IP
    }
  }
  
  return `ip:${ip}`
}

// 验证User-Agent
function isValidUserAgent(userAgent: string, config: SecurityConfig): boolean {
  if (!userAgent) return false
  
  const lowerUA = userAgent.toLowerCase()
  return !config.blockedUserAgents.some(blocked => lowerUA.includes(blocked))
}

// CORS验证
function validateCORS(request: NextRequest, config: SecurityConfig): boolean {
  const origin = request.headers.get('origin')
  
  if (!origin) return true // 同源请求
  
  return config.allowedOrigins.includes(origin) || 
         config.allowedOrigins.includes('*')
}

// 请求大小验证
function validateRequestSize(request: NextRequest, maxSize: number = 10 * 1024 * 1024): boolean {
  const contentLength = request.headers.get('content-length')
  
  if (!contentLength) return true
  
  return parseInt(contentLength) <= maxSize
}

// 检查可疑请求模式
function detectSuspiciousPatterns(request: NextRequest): string[] {
  const suspiciousPatterns: string[] = []
  const url = request.url.toLowerCase()
  const userAgent = request.headers.get('user-agent')?.toLowerCase() || ''
  
  // SQL注入模式
  const sqlPatterns = [
    'union select',
    'drop table',
    'insert into',
    'delete from',
    'update set',
    'exec(',
    'script>',
  ]
  
  if (sqlPatterns.some(pattern => url.includes(pattern))) {
    suspiciousPatterns.push('sql_injection_attempt')
  }
  
  // XSS模式
  const xssPatterns = [
    '<script',
    'javascript:',
    'onerror=',
    'onload=',
    'eval(',
  ]
  
  if (xssPatterns.some(pattern => url.includes(pattern))) {
    suspiciousPatterns.push('xss_attempt')
  }
  
  // 路径遍历
  if (url.includes('../') || url.includes('..\\')) {
    suspiciousPatterns.push('path_traversal_attempt')
  }
  
  // 自动化工具检测
  const botPatterns = [
    'python',
    'curl',
    'wget',
    'postman',
    'insomnia',
  ]
  
  if (botPatterns.some(pattern => userAgent.includes(pattern))) {
    suspiciousPatterns.push('automated_tool')
  }
  
  return suspiciousPatterns
}

// 主要安全中间件
export function createSecurityMiddleware(config: SecurityConfig = defaultConfig) {
  return async function securityMiddleware(
    request: NextRequest,
    next: () => Promise<NextResponse>
  ): Promise<NextResponse> {
    const startTime = Date.now()
    const ip = getClientIP(request)
    const userAgent = request.headers.get('user-agent') || ''
    const identifier = getUserIdentifier(request)
    
    try {
      // 1. IP黑名单检查
      if (config.enableIPBlocking && IPSecurity.isBlocked(ip)) {
        logSecurityEvent('blocked_ip_access', { ip, userAgent }, 'high')
        return new NextResponse('Access Denied', { status: 403 })
      }
      
      // 2. User-Agent验证
      if (!isValidUserAgent(userAgent, config)) {
        logSecurityEvent('blocked_user_agent', { ip, userAgent }, 'medium')
        return new NextResponse('Invalid User Agent', { status: 403 })
      }
      
      // 3. 请求大小验证
      if (!validateRequestSize(request)) {
        logSecurityEvent('request_too_large', { ip, userAgent }, 'medium')
        return new NextResponse('Request Too Large', { status: 413 })
      }
      
      // 4. CORS验证
      if (config.enableCORS && !validateCORS(request, config)) {
        logSecurityEvent('cors_violation', { 
          ip, 
          userAgent, 
          origin: request.headers.get('origin') 
        }, 'medium')
        return new NextResponse('CORS Policy Violation', { status: 403 })
      }
      
      // 5. 速率限制
      if (config.enableRateLimit) {
        if (!RateLimiter.isAllowed(identifier, config.maxRequestsPerMinute, 60000)) {
          logSecurityEvent('rate_limit_exceeded', { ip, userAgent, identifier }, 'high')
          
          const remaining = RateLimiter.getRemainingRequests(identifier, config.maxRequestsPerMinute)
          return new NextResponse('Rate Limit Exceeded', {
            status: 429,
            headers: {
              'X-RateLimit-Limit': config.maxRequestsPerMinute.toString(),
              'X-RateLimit-Remaining': remaining.toString(),
              'X-RateLimit-Reset': new Date(Date.now() + 60000).toISOString(),
            },
          })
        }
      }
      
      // 6. CSRF保护（POST/PUT/DELETE请求）
      if (config.enableCSRF && ['POST', 'PUT', 'DELETE'].includes(request.method)) {
        const csrfToken = request.headers.get('x-csrf-token')
        const sessionId = request.headers.get('x-session-id') || identifier
        
        if (!csrfToken || !CSRFProtection.verifyToken(csrfToken, sessionId)) {
          logSecurityEvent('csrf_token_invalid', { ip, userAgent }, 'high')
          return new NextResponse('CSRF Token Invalid', { status: 403 })
        }
      }
      
      // 7. 可疑模式检测
      const suspiciousPatterns = detectSuspiciousPatterns(request)
      if (suspiciousPatterns.length > 0) {
        logSecurityEvent('suspicious_request_pattern', {
          ip,
          userAgent,
          patterns: suspiciousPatterns,
          url: request.url,
        }, 'high')
        
        // 对于高风险模式，直接阻止
        const highRiskPatterns = ['sql_injection_attempt', 'xss_attempt', 'path_traversal_attempt']
        if (suspiciousPatterns.some(pattern => highRiskPatterns.includes(pattern))) {
          return new NextResponse('Malicious Request Detected', { status: 403 })
        }
      }
      
      // 8. 执行下一个中间件或处理器
      const response = await next()
      
      // 9. 添加安全响应头
      const secureResponse = new NextResponse(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      })
      
      // 安全头部
      secureResponse.headers.set('X-Content-Type-Options', 'nosniff')
      secureResponse.headers.set('X-Frame-Options', 'DENY')
      secureResponse.headers.set('X-XSS-Protection', '1; mode=block')
      secureResponse.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin')
      secureResponse.headers.set('Permissions-Policy', 'camera=(), microphone=(), geolocation=()')
      
      // CORS头部
      if (config.enableCORS) {
        const origin = request.headers.get('origin')
        if (origin && config.allowedOrigins.includes(origin)) {
          secureResponse.headers.set('Access-Control-Allow-Origin', origin)
          secureResponse.headers.set('Access-Control-Allow-Credentials', 'true')
          secureResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
          secureResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-CSRF-Token, X-Session-ID')
        }
      }
      
      // 性能头部
      const processingTime = Date.now() - startTime
      secureResponse.headers.set('X-Response-Time', `${processingTime}ms`)
      
      // 速率限制头部
      if (config.enableRateLimit) {
        const remaining = RateLimiter.getRemainingRequests(identifier, config.maxRequestsPerMinute)
        secureResponse.headers.set('X-RateLimit-Limit', config.maxRequestsPerMinute.toString())
        secureResponse.headers.set('X-RateLimit-Remaining', remaining.toString())
      }
      
      return secureResponse
      
    } catch (error) {
      logSecurityEvent('middleware_error', {
        ip,
        userAgent,
        error: error instanceof Error ? error.message : 'Unknown error',
      }, 'critical')
      
      return new NextResponse('Internal Server Error', { status: 500 })
    }
  }
}

// 预设的安全配置
export const SecurityConfigs = {
  // 开发环境配置
  development: {
    ...defaultConfig,
    enableRateLimit: false,
    enableCSRF: false,
    maxRequestsPerMinute: 1000,
    allowedOrigins: ['*'],
  },
  
  // 生产环境配置
  production: {
    ...defaultConfig,
    enableRateLimit: true,
    enableCSRF: true,
    maxRequestsPerMinute: 60,
    allowedOrigins: [
      'https://your-domain.com',
      'https://www.your-domain.com',
    ],
  },
  
  // API专用配置
  api: {
    ...defaultConfig,
    enableRateLimit: true,
    enableCSRF: false, // API通常使用token认证
    maxRequestsPerMinute: 200,
    allowedOrigins: ['*'], // API通常允许跨域
  },
}

// 导出默认中间件
export const securityMiddleware = createSecurityMiddleware(
  process.env.NODE_ENV === 'production' 
    ? SecurityConfigs.production 
    : SecurityConfigs.development
)
