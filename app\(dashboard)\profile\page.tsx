/**
 * 商家资料页面
 * 显示和编辑商家信息
 */

'use client'

import { useState, useEffect } from 'react'
import { PageContainer } from '@/components/layouts/page-container'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { MerchantForm } from '@/components/forms/merchant-form'
import { LogoUpload } from '@/components/forms/logo-upload'
import { MerchantStatusForm } from '@/components/forms/merchant-status'
import { useAuth } from '@/lib/auth/context'
import { getCurrentMerchant, getMerchantStats } from '@/lib/actions/merchant'
import { 
  BUSINESS_TYPE_CONFIG, 
  MERCHANT_STATUS_CONFIG,
  type Merchant,
  type MerchantStats
} from '@/types/merchant'

export default function ProfilePage() {
  const { user, refreshUser } = useAuth()
  const [merchant, setMerchant] = useState<Merchant | null>(null)
  const [stats, setStats] = useState<MerchantStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState<'info' | 'logo' | 'status'>('info')

  useEffect(() => {
    loadMerchantData()
  }, [])

  const loadMerchantData = async () => {
    setIsLoading(true)
    
    try {
      const [merchantResult, statsResult] = await Promise.all([
        getCurrentMerchant(),
        getMerchantStats()
      ])

      if (merchantResult.success && merchantResult.data) {
        setMerchant(merchantResult.data)
      }

      if (statsResult.success && statsResult.data) {
        setStats(statsResult.data)
      }
    } catch (error) {
      console.error('Load merchant data error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSuccess = async () => {
    await refreshUser()
    await loadMerchantData()
  }

  if (isLoading) {
    return (
      <PageContainer title="商家资料" description="管理您的商家信息和设置">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </PageContainer>
    )
  }

  if (!merchant) {
    return (
      <PageContainer title="商家资料" description="管理您的商家信息和设置">
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <p className="text-muted-foreground">未找到商家信息</p>
              <Button onClick={loadMerchantData} className="mt-4">
                重新加载
              </Button>
            </div>
          </CardContent>
        </Card>
      </PageContainer>
    )
  }

  return (
    <PageContainer
      title="商家资料"
      description="管理您的商家信息和设置"
      breadcrumbs={[
        { title: '仪表板', href: '/dashboard' },
        { title: '商家资料' }
      ]}
    >
      <div className="space-y-6">
        {/* 商家概览 */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center space-x-3">
                  {merchant.logo && (
                    <img
                      src={merchant.logo}
                      alt={merchant.name}
                      className="w-10 h-10 rounded-lg object-cover"
                    />
                  )}
                  <span>{merchant.name}</span>
                </CardTitle>
                <CardDescription className="mt-2">
                  {merchant.description || '暂无商家描述'}
                </CardDescription>
              </div>
              <Badge 
                variant={
                  merchant.status === 'active' ? 'success' : 
                  merchant.status === 'inactive' ? 'warning' : 'destructive'
                }
              >
                {MERCHANT_STATUS_CONFIG[merchant.status].label}
              </Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* 业务类型 */}
              <div>
                <h4 className="text-sm font-medium mb-2">业务类型</h4>
                <div className="flex flex-wrap gap-1">
                  {merchant.business_types?.map((type) => (
                    <Badge key={type} variant="secondary" className="text-xs">
                      {BUSINESS_TYPE_CONFIG[type]?.icon} {BUSINESS_TYPE_CONFIG[type]?.label}
                    </Badge>
                  ))}
                </div>
              </div>

              {/* 联系信息 */}
              <div>
                <h4 className="text-sm font-medium mb-2">联系信息</h4>
                <div className="space-y-1 text-sm text-muted-foreground">
                  {(merchant.contact_info as any)?.phone && (
                    <p>📞 {(merchant.contact_info as any).phone}</p>
                  )}
                  {(merchant.contact_info as any)?.email && (
                    <p>📧 {(merchant.contact_info as any).email}</p>
                  )}
                  {(merchant.contact_info as any)?.address && (
                    <p>📍 {(merchant.contact_info as any).address}</p>
                  )}
                </div>
              </div>

              {/* 统计信息 */}
              {stats && (
                <div>
                  <h4 className="text-sm font-medium mb-2">业务统计</h4>
                  <div className="space-y-1 text-sm text-muted-foreground">
                    <p>总订单: {stats.total_orders}</p>
                    <p>待处理: {stats.pending_orders}</p>
                    <p>总收入: ¥{stats.total_revenue.toFixed(2)}</p>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 标签页导航 */}
        <div className="border-b">
          <nav className="flex space-x-8">
            {[
              { key: 'info', label: '基础信息' },
              { key: 'logo', label: 'Logo管理' },
              { key: 'status', label: '状态管理' },
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.key
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground hover:border-border'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* 标签页内容 */}
        <div>
          {activeTab === 'info' && (
            <MerchantForm merchant={merchant} onSuccess={handleSuccess} />
          )}
          
          {activeTab === 'logo' && (
            <LogoUpload 
              currentLogo={merchant.logo || undefined} 
              onSuccess={handleSuccess}
            />
          )}
          
          {activeTab === 'status' && (
            <MerchantStatusForm 
              currentStatus={merchant.status} 
              onSuccess={handleSuccess}
            />
          )}
        </div>
      </div>
    </PageContainer>
  )
}
