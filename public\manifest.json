{"name": "柒零支付连锁平台", "short_name": "柒零连锁", "description": "综合性连锁商业管理平台，集酒店、餐饮、零售、支付于一体", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#3b82f6", "orientation": "portrait-primary", "scope": "/", "lang": "zh-CN", "categories": ["business", "productivity", "finance"], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "maskable any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "maskable any"}], "shortcuts": [{"name": "仪表板", "short_name": "仪表板", "description": "查看业务概览", "url": "/dashboard", "icons": [{"src": "/icons/dashboard-96x96.png", "sizes": "96x96"}]}, {"name": "订单管理", "short_name": "订单", "description": "管理订单", "url": "/orders", "icons": [{"src": "/icons/orders-96x96.png", "sizes": "96x96"}]}, {"name": "支付管理", "short_name": "支付", "description": "处理支付", "url": "/payments", "icons": [{"src": "/icons/payments-96x96.png", "sizes": "96x96"}]}, {"name": "数据统计", "short_name": "统计", "description": "查看数据分析", "url": "/analytics", "icons": [{"src": "/icons/analytics-96x96.png", "sizes": "96x96"}]}], "screenshots": [{"src": "/screenshots/desktop-dashboard.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "桌面端仪表板"}, {"src": "/screenshots/mobile-dashboard.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "移动端仪表板"}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}}