# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54321
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# 数据库配置 (可选，用于直接连接)
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54322/postgres

# Supabase 本地开发配置
SUPABASE_DB_PASSWORD=postgres
SUPABASE_JWT_SECRET=your-super-secret-jwt-token-with-at-least-32-characters-long

# 支付配置
# 柒零支付
QILING_PAYMENT_API_KEY=your_qiling_payment_api_key
QILING_PAYMENT_SECRET=your_qiling_payment_secret

# 支付宝
ALIPAY_APP_ID=your_alipay_app_id
ALIPAY_PRIVATE_KEY=your_alipay_private_key
ALIPAY_PUBLIC_KEY=your_alipay_public_key

# 微信支付
WECHAT_PAY_APP_ID=your_wechat_app_id
WECHAT_PAY_MCH_ID=your_wechat_mch_id
WECHAT_PAY_API_KEY=your_wechat_api_key

# 应用配置
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your_nextauth_secret

# 文件上传配置
UPLOAD_MAX_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp

# 邮件配置 (可选)
SMTP_HOST=your_smtp_host
SMTP_PORT=587
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password

# 监控配置 (可选)
SENTRY_DSN=your_sentry_dsn
VERCEL_ANALYTICS_ID=your_vercel_analytics_id
