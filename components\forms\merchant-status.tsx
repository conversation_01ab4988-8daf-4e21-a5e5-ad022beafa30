/**
 * 商家状态管理组件
 * 用于更新商家营业状态
 */

'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { merchantStatusSchema } from '@/lib/validations/merchant'
import { updateMerchantStatus } from '@/lib/actions/merchant'
import { 
  MERCHANT_STATUS_CONFIG,
  type MerchantStatusFormData,
  type MerchantStatus
} from '@/types/merchant'

interface MerchantStatusProps {
  currentStatus: MerchantStatus
  onSuccess?: () => void
  className?: string
}

export function MerchantStatusForm({ currentStatus, onSuccess, className }: MerchantStatusProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<MerchantStatusFormData>({
    resolver: zodResolver(merchantStatusSchema),
    defaultValues: {
      status: currentStatus,
    },
  })

  const selectedStatus = watch('status')

  const onSubmit = async (data: MerchantStatusFormData) => {
    if (data.status === currentStatus) {
      setError('请选择不同的状态')
      return
    }

    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      const result = await updateMerchantStatus(data)

      if (result.success) {
        setSuccess(result.message || '商家状态更新成功')
        onSuccess?.()
      } else {
        setError(result.error || '更新失败')
      }
    } catch (err) {
      setError('更新失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>商家状态</CardTitle>
        <CardDescription>
          管理您的商家营业状态，这将影响客户是否能够下单
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* 错误提示 */}
          {error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
              <p className="text-sm text-destructive">{error}</p>
            </div>
          )}

          {/* 成功提示 */}
          {success && (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md">
              <p className="text-sm text-green-600">{success}</p>
            </div>
          )}

          {/* 当前状态显示 */}
          <div className="flex items-center justify-between p-4 bg-muted/50 rounded-lg">
            <div>
              <p className="text-sm font-medium">当前状态</p>
              <p className="text-xs text-muted-foreground">
                {MERCHANT_STATUS_CONFIG[currentStatus].description}
              </p>
            </div>
            <Badge 
              variant={
                currentStatus === 'active' ? 'success' : 
                currentStatus === 'inactive' ? 'warning' : 'destructive'
              }
            >
              {MERCHANT_STATUS_CONFIG[currentStatus].label}
            </Badge>
          </div>

          {/* 状态选择 */}
          <div className="space-y-3">
            <label className="text-sm font-medium">选择新状态</label>
            <div className="space-y-3">
              {Object.entries(MERCHANT_STATUS_CONFIG).map(([status, config]) => (
                <label
                  key={status}
                  className={`flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedStatus === status
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:border-primary/50'
                  }`}
                >
                  <input
                    {...register('status')}
                    type="radio"
                    value={status}
                    className="h-4 w-4 text-primary focus:ring-primary border-border"
                    disabled={isLoading}
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{config.label}</span>
                      <Badge 
                        variant={
                          status === 'active' ? 'success' : 
                          status === 'inactive' ? 'warning' : 'destructive'
                        }
                        className="text-xs"
                      >
                        {status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {config.description}
                    </p>
                  </div>
                </label>
              ))}
            </div>
            {errors.status && (
              <p className="text-sm text-destructive">{errors.status.message}</p>
            )}
          </div>

          {/* 状态变更说明 */}
          {selectedStatus && selectedStatus !== currentStatus && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">状态变更说明</h4>
              <div className="text-sm text-blue-800 space-y-1">
                {selectedStatus === 'active' && (
                  <>
                    <p>• 商家将恢复正常营业</p>
                    <p>• 客户可以正常下单和预订</p>
                    <p>• 所有业务功能将重新启用</p>
                  </>
                )}
                {selectedStatus === 'inactive' && (
                  <>
                    <p>• 商家将暂停营业</p>
                    <p>• 客户无法下新订单</p>
                    <p>• 现有订单仍可正常处理</p>
                  </>
                )}
                {selectedStatus === 'suspended' && (
                  <>
                    <p>• 商家账户将被冻结</p>
                    <p>• 所有业务功能将被禁用</p>
                    <p>• 需要联系客服解除冻结</p>
                  </>
                )}
              </div>
            </div>
          )}

          {/* 提交按钮 */}
          <div className="flex justify-end">
            <Button 
              type="submit" 
              disabled={isLoading || selectedStatus === currentStatus}
            >
              {isLoading ? '更新中...' : '更新状态'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
