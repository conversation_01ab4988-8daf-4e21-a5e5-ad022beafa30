/**
 * 用户认证状态管理
 * 使用 React Context 管理用户登录状态
 */

'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { createClient } from '@/lib/supabase/client'
import { getCurrentUser } from '@/lib/auth/actions'
import type { User } from '@supabase/supabase-js'

// 用户信息类型
interface UserInfo {
  id: string
  email: string
  name: string
  phone: string
  emailConfirmed: boolean
  createdAt: string
  merchant: any | null
}

// 认证上下文类型
interface AuthContextType {
  user: UserInfo | null
  loading: boolean
  signOut: () => Promise<void>
  refreshUser: () => Promise<void>
}

// 创建认证上下文
const AuthContext = createContext<AuthContextType | undefined>(undefined)

// 认证提供者组件
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserInfo | null>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createClient()

  // 获取用户信息
  const fetchUser = async () => {
    try {
      const userInfo = await getCurrentUser()
      setUser(userInfo)
    } catch (error) {
      console.error('Error fetching user:', error)
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  // 刷新用户信息
  const refreshUser = async () => {
    await fetchUser()
  }

  // 登出
  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        console.error('Error signing out:', error)
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('Error signing out:', error)
    }
  }

  // 监听认证状态变化
  useEffect(() => {
    // 初始加载用户信息
    fetchUser()

    // 监听认证状态变化
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (event === 'SIGNED_IN' && session) {
        // 用户登录
        await fetchUser()
      } else if (event === 'SIGNED_OUT') {
        // 用户登出
        setUser(null)
        setLoading(false)
      } else if (event === 'TOKEN_REFRESHED' && session) {
        // 令牌刷新
        await fetchUser()
      }
    })

    return () => subscription.unsubscribe()
  }, [])

  const value = {
    user,
    loading,
    signOut,
    refreshUser,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

// 使用认证上下文的 Hook
export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// 检查用户是否已登录的 Hook
export function useRequireAuth() {
  const { user, loading } = useAuth()
  
  useEffect(() => {
    if (!loading && !user) {
      // 重定向到登录页面
      window.location.href = '/login'
    }
  }, [user, loading])

  return { user, loading }
}
