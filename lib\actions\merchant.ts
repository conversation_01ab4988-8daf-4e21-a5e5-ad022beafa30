/**
 * 商家相关的 Server Actions
 * 处理商家信息的增删改查操作
 */

'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/lib/supabase/server'
import { merchantQueries } from '@/lib/supabase/queries'
import { getCurrentUser } from '@/lib/auth/actions'
import type { 
  MerchantUpdateFormData, 
  MerchantStatusFormData,
  MerchantActionResult 
} from '@/types/merchant'

// 获取当前商家信息
export async function getCurrentMerchant(): Promise<MerchantActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return {
        success: false,
        error: '用户未登录',
      }
    }

    const merchant = await merchantQueries.getCurrentMerchant(user.id)

    if (!merchant) {
      return {
        success: false,
        error: '未找到商家信息',
      }
    }

    return {
      success: true,
      data: merchant,
    }
  } catch (error) {
    console.error('Get current merchant error:', error)
    return {
      success: false,
      error: '获取商家信息失败',
    }
  }
}

// 更新商家信息
export async function updateMerchant(formData: MerchantUpdateFormData): Promise<MerchantActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const updateData = {
      name: formData.name,
      description: formData.description || null,
      business_types: formData.business_types,
      contact_info: formData.contact_info,
      business_hours: formData.business_hours || null,
      logo: formData.logo || null,
    }

    const updatedMerchant = await merchantQueries.updateMerchant(user.merchant.id, updateData)

    if (!updatedMerchant) {
      return {
        success: false,
        error: '更新商家信息失败',
      }
    }

    revalidatePath('/profile')
    revalidatePath('/dashboard')

    return {
      success: true,
      data: updatedMerchant,
      message: '商家信息更新成功',
    }
  } catch (error) {
    console.error('Update merchant error:', error)
    return {
      success: false,
      error: '更新商家信息失败，请稍后重试',
    }
  }
}

// 更新商家状态
export async function updateMerchantStatus(formData: MerchantStatusFormData): Promise<MerchantActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const updatedMerchant = await merchantQueries.updateMerchant(user.merchant.id, {
      status: formData.status,
    })

    if (!updatedMerchant) {
      return {
        success: false,
        error: '更新商家状态失败',
      }
    }

    revalidatePath('/profile')
    revalidatePath('/dashboard')

    return {
      success: true,
      data: updatedMerchant,
      message: '商家状态更新成功',
    }
  } catch (error) {
    console.error('Update merchant status error:', error)
    return {
      success: false,
      error: '更新商家状态失败，请稍后重试',
    }
  }
}

// 上传商家Logo
export async function uploadMerchantLogo(formData: FormData): Promise<MerchantActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const file = formData.get('file') as File
    
    if (!file) {
      return {
        success: false,
        error: '请选择要上传的文件',
      }
    }

    // 验证文件类型和大小
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return {
        success: false,
        error: '只支持 JPEG、PNG、WebP 格式的图片',
      }
    }

    if (file.size > 5 * 1024 * 1024) {
      return {
        success: false,
        error: '图片大小不能超过5MB',
      }
    }

    const supabase = createClient()
    
    // 生成文件名
    const fileExt = file.name.split('.').pop()
    const fileName = `${user.merchant.id}/logo.${fileExt}`

    // 上传到 Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('merchant-assets')
      .upload(fileName, file, {
        upsert: true,
      })

    if (uploadError) {
      console.error('Upload error:', uploadError)
      return {
        success: false,
        error: '文件上传失败',
      }
    }

    // 获取公共URL
    const { data: urlData } = supabase.storage
      .from('merchant-assets')
      .getPublicUrl(fileName)

    if (!urlData.publicUrl) {
      return {
        success: false,
        error: '获取文件URL失败',
      }
    }

    // 更新商家Logo URL
    const updatedMerchant = await merchantQueries.updateMerchant(user.merchant.id, {
      logo: urlData.publicUrl,
    })

    if (!updatedMerchant) {
      return {
        success: false,
        error: '更新商家Logo失败',
      }
    }

    revalidatePath('/profile')
    revalidatePath('/dashboard')

    return {
      success: true,
      data: updatedMerchant,
      message: 'Logo上传成功',
    }
  } catch (error) {
    console.error('Upload merchant logo error:', error)
    return {
      success: false,
      error: '上传Logo失败，请稍后重试',
    }
  }
}

// 获取商家统计信息
export async function getMerchantStats() {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const stats = await merchantQueries.getMerchantStats(user.merchant.id)

    return {
      success: true,
      data: stats,
    }
  } catch (error) {
    console.error('Get merchant stats error:', error)
    return {
      success: false,
      error: '获取统计信息失败',
    }
  }
}
