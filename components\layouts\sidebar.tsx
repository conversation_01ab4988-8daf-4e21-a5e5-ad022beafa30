/**
 * 侧边栏组件
 * 主要导航侧边栏，支持响应式设计
 */

'use client'

import { useState } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  Home,
  Hotel,
  UtensilsCrossed,
  Package,
  ShoppingCart,
  CreditCard,
  BarChart3,
  Settings,
  User,
  Menu,
  X,
  ChevronDown,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/lib/auth/context'

interface SidebarProps {
  className?: string
}

interface NavItem {
  title: string
  href: string
  icon: React.ComponentType<{ className?: string }>
  badge?: string
  children?: NavItem[]
  businessTypes?: string[]
}

const navigation: NavItem[] = [
  {
    title: '仪表板',
    href: '/dashboard',
    icon: Home,
  },
  {
    title: '酒店管理',
    href: '/hotels',
    icon: Hotel,
    businessTypes: ['hotel'],
    children: [
      { title: '酒店列表', href: '/hotels', icon: Hotel },
      { title: '房间管理', href: '/hotels/rooms', icon: Hotel },
      { title: '预订管理', href: '/hotels/bookings', icon: Hotel },
    ],
  },
  {
    title: '餐饮管理',
    href: '/restaurants',
    icon: UtensilsCrossed,
    businessTypes: ['restaurant'],
    children: [
      { title: '餐厅列表', href: '/restaurants', icon: UtensilsCrossed },
      { title: '菜单管理', href: '/restaurants/menu', icon: UtensilsCrossed },
      { title: '订餐管理', href: '/restaurants/orders', icon: UtensilsCrossed },
    ],
  },
  {
    title: '商品管理',
    href: '/products',
    icon: Package,
    businessTypes: ['product'],
    children: [
      { title: '商品列表', href: '/products', icon: Package },
      { title: '商品分类', href: '/products/categories', icon: Package },
      { title: '库存管理', href: '/products/inventory', icon: Package },
    ],
  },
  {
    title: '订单管理',
    href: '/orders',
    icon: ShoppingCart,
    badge: '新',
  },
  {
    title: '支付管理',
    href: '/payments',
    icon: CreditCard,
  },
  {
    title: '数据统计',
    href: '/analytics',
    icon: BarChart3,
  },
  {
    title: '商家资料',
    href: '/profile',
    icon: User,
  },
  {
    title: '系统设置',
    href: '/settings',
    icon: Settings,
  },
]

export function Sidebar({ className }: SidebarProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const pathname = usePathname()
  const { user } = useAuth()

  const toggleExpanded = (href: string) => {
    setExpandedItems(prev => 
      prev.includes(href) 
        ? prev.filter(item => item !== href)
        : [...prev, href]
    )
  }

  const isItemVisible = (item: NavItem) => {
    if (!item.businessTypes) return true
    if (!user?.merchant?.business_types) return false
    return item.businessTypes.some(type => 
      user.merchant.business_types.includes(type)
    )
  }

  const isActive = (href: string) => {
    if (href === '/dashboard') {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  const renderNavItem = (item: NavItem, level = 0) => {
    if (!isItemVisible(item)) return null

    const hasChildren = item.children && item.children.length > 0
    const isExpanded = expandedItems.includes(item.href)
    const active = isActive(item.href)

    return (
      <div key={item.href}>
        <div className="flex items-center">
          <Link
            href={item.href}
            className={cn(
              'flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors flex-1',
              level > 0 && 'ml-6',
              active
                ? 'bg-primary text-primary-foreground'
                : 'text-muted-foreground hover:bg-accent hover:text-accent-foreground'
            )}
          >
            <item.icon className="h-4 w-4" />
            <span>{item.title}</span>
            {item.badge && (
              <Badge variant="secondary" className="ml-auto">
                {item.badge}
              </Badge>
            )}
          </Link>
          
          {hasChildren && (
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8"
              onClick={() => toggleExpanded(item.href)}
            >
              {isExpanded ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          )}
        </div>

        {hasChildren && isExpanded && (
          <div className="mt-1 space-y-1">
            {item.children?.map(child => renderNavItem(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <>
      {/* 移动端菜单按钮 */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setIsOpen(!isOpen)}
      >
        {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </Button>

      {/* 移动端遮罩 */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* 侧边栏 */}
      <div
        className={cn(
          'fixed left-0 top-0 z-40 h-full w-64 transform bg-background border-r transition-transform duration-200 ease-in-out md:relative md:translate-x-0',
          isOpen ? 'translate-x-0' : '-translate-x-full',
          className
        )}
      >
        <div className="flex h-full flex-col">
          {/* 头部 */}
          <div className="flex h-16 items-center border-b px-6">
            <Link href="/dashboard" className="flex items-center gap-2">
              <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
                <span className="text-sm font-bold">柒</span>
              </div>
              <span className="font-semibold">柒零支付连锁</span>
            </Link>
          </div>

          {/* 导航菜单 */}
          <nav className="flex-1 space-y-1 p-4 overflow-y-auto">
            {navigation.map(item => renderNavItem(item))}
          </nav>

          {/* 底部用户信息 */}
          {user && (
            <div className="border-t p-4">
              <div className="flex items-center gap-3">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-primary text-primary-foreground text-sm font-medium">
                  {user.name ? user.name[0].toUpperCase() : user.email[0].toUpperCase()}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">
                    {user.name || user.email}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {user.merchant?.name || '商家用户'}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  )
}
