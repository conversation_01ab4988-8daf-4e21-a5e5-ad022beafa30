/**
 * 商品相关的 Server Actions
 * 处理商品和分类的增删改查操作
 */

'use server'

import { revalidatePath } from 'next/cache'
import { createClient } from '@/lib/supabase/server'
import { getCurrentUser } from '@/lib/auth/actions'
import type { 
  ProductFormData, 
  ProductCategoryFormData,
  ProductVariantFormData,
  StockFormData,
  ProductSearchParams,
  ProductActionResult,
  Product,
  ProductCategory,
  ProductVariant
} from '@/types/product'

// 获取当前商家的商品列表
export async function getProducts(params?: ProductSearchParams): Promise<ProductActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const supabase = createClient()
    let query = supabase
      .from('products')
      .select(`
        *,
        category:product_categories(id, name),
        variants:product_variants(id, name, price, stock_quantity)
      `)
      .eq('merchant_id', user.merchant.id)

    // 应用搜索条件
    if (params?.query) {
      query = query.or(`name.ilike.%${params.query}%,description.ilike.%${params.query}%,sku.ilike.%${params.query}%`)
    }

    if (params?.category_id) {
      query = query.eq('category_id', params.category_id)
    }

    if (params?.product_type && params.product_type.length > 0) {
      query = query.in('product_type', params.product_type)
    }

    if (params?.status && params.status.length > 0) {
      query = query.in('status', params.status)
    }

    if (params?.min_price) {
      query = query.gte('base_price', params.min_price)
    }

    if (params?.max_price) {
      query = query.lte('base_price', params.max_price)
    }

    if (params?.brand) {
      query = query.ilike('brand', `%${params.brand}%`)
    }

    if (params?.is_featured !== undefined) {
      query = query.eq('is_featured', params.is_featured)
    }

    if (params?.is_digital !== undefined) {
      query = query.eq('is_digital', params.is_digital)
    }

    // 排序
    const sortBy = params?.sort_by || 'created_at'
    const sortOrder = params?.sort_order || 'desc'
    query = query.order(sortBy, { ascending: sortOrder === 'asc' })

    // 分页
    const page = params?.page || 1
    const limit = params?.limit || 20
    const offset = (page - 1) * limit
    query = query.range(offset, offset + limit - 1)

    const { data: products, error } = await query

    if (error) {
      console.error('Get products error:', error)
      return {
        success: false,
        error: '获取商品列表失败',
      }
    }

    return {
      success: true,
      data: products as any,
    }
  } catch (error) {
    console.error('Get products error:', error)
    return {
      success: false,
      error: '获取商品列表失败',
    }
  }
}

// 获取商品详情
export async function getProductById(id: string): Promise<ProductActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const supabase = createClient()
    const { data: product, error } = await supabase
      .from('products')
      .select(`
        *,
        category:product_categories(*),
        variants:product_variants(*)
      `)
      .eq('id', id)
      .eq('merchant_id', user.merchant.id)
      .single()

    if (error) {
      console.error('Get product by id error:', error)
      return {
        success: false,
        error: '获取商品详情失败',
      }
    }

    if (!product) {
      return {
        success: false,
        error: '未找到商品信息',
      }
    }

    return {
      success: true,
      data: product as any,
    }
  } catch (error) {
    console.error('Get product by id error:', error)
    return {
      success: false,
      error: '获取商品详情失败',
    }
  }
}

// 创建商品
export async function createProduct(formData: ProductFormData): Promise<ProductActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    // 检查商家是否有商品业务类型
    if (!user.merchant.business_types?.includes('product')) {
      return {
        success: false,
        error: '您的商家未开通商品业务',
      }
    }

    const productData = {
      merchant_id: user.merchant.id,
      name: formData.name,
      description: formData.description || null,
      short_description: formData.short_description || null,
      category_id: formData.category_id || null,
      product_type: formData.product_type,
      sku: formData.sku || null,
      barcode: formData.barcode || null,
      brand: formData.brand || null,
      model: formData.model || null,
      images: formData.images || [],
      base_price: formData.base_price,
      sale_price: formData.sale_price || null,
      cost_price: formData.cost_price || null,
      weight: formData.weight || null,
      dimensions: formData.dimensions || null,
      attributes: formData.attributes || [],
      specifications: formData.specifications || [],
      tags: formData.tags || [],
      is_featured: formData.is_featured || false,
      is_digital: formData.is_digital || false,
      requires_shipping: formData.requires_shipping !== false,
      is_active: formData.is_active !== false,
      meta_title: formData.meta_title || null,
      meta_description: formData.meta_description || null,
      seo_keywords: formData.seo_keywords || [],
    }

    const supabase = createClient()
    const { data: product, error } = await supabase
      .from('products')
      .insert(productData)
      .select()
      .single()

    if (error) {
      console.error('Create product error:', error)
      return {
        success: false,
        error: '创建商品失败',
      }
    }

    revalidatePath('/products')

    return {
      success: true,
      data: product,
      message: '商品创建成功',
    }
  } catch (error) {
    console.error('Create product error:', error)
    return {
      success: false,
      error: '创建商品失败，请稍后重试',
    }
  }
}

// 更新商品信息
export async function updateProduct(id: string, formData: ProductFormData): Promise<ProductActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    // 验证商品所有权
    const supabase = createClient()
    const { data: existingProduct, error: fetchError } = await supabase
      .from('products')
      .select('id, merchant_id')
      .eq('id', id)
      .eq('merchant_id', user.merchant.id)
      .single()

    if (fetchError || !existingProduct) {
      return {
        success: false,
        error: '无权修改该商品信息',
      }
    }

    const updateData = {
      name: formData.name,
      description: formData.description || null,
      short_description: formData.short_description || null,
      category_id: formData.category_id || null,
      product_type: formData.product_type,
      sku: formData.sku || null,
      barcode: formData.barcode || null,
      brand: formData.brand || null,
      model: formData.model || null,
      images: formData.images || [],
      base_price: formData.base_price,
      sale_price: formData.sale_price || null,
      cost_price: formData.cost_price || null,
      weight: formData.weight || null,
      dimensions: formData.dimensions || null,
      attributes: formData.attributes || [],
      specifications: formData.specifications || [],
      tags: formData.tags || [],
      is_featured: formData.is_featured || false,
      is_digital: formData.is_digital || false,
      requires_shipping: formData.requires_shipping !== false,
      is_active: formData.is_active !== false,
      meta_title: formData.meta_title || null,
      meta_description: formData.meta_description || null,
      seo_keywords: formData.seo_keywords || [],
    }

    const { data: product, error: updateError } = await supabase
      .from('products')
      .update(updateData)
      .eq('id', id)
      .select()
      .single()

    if (updateError) {
      console.error('Update product error:', updateError)
      return {
        success: false,
        error: '更新商品信息失败',
      }
    }

    revalidatePath('/products')
    revalidatePath(`/products/${id}`)

    return {
      success: true,
      data: product,
      message: '商品信息更新成功',
    }
  } catch (error) {
    console.error('Update product error:', error)
    return {
      success: false,
      error: '更新商品信息失败，请稍后重试',
    }
  }
}

// 删除商品
export async function deleteProduct(id: string): Promise<ProductActionResult> {
  try {
    const user = await getCurrentUser()
    
    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    // 验证商品所有权
    const supabase = createClient()
    const { data: existingProduct, error: fetchError } = await supabase
      .from('products')
      .select('id, merchant_id')
      .eq('id', id)
      .eq('merchant_id', user.merchant.id)
      .single()

    if (fetchError || !existingProduct) {
      return {
        success: false,
        error: '无权删除该商品',
      }
    }

    const { error: deleteError } = await supabase
      .from('products')
      .delete()
      .eq('id', id)

    if (deleteError) {
      console.error('Delete product error:', deleteError)
      return {
        success: false,
        error: '删除商品失败',
      }
    }

    revalidatePath('/products')

    return {
      success: true,
      message: '商品删除成功',
    }
  } catch (error) {
    console.error('Delete product error:', error)
    return {
      success: false,
      error: '删除商品失败，请稍后重试',
    }
  }
}

// 获取商品分类
export async function getProductCategories(): Promise<ProductActionResult> {
  try {
    const user = await getCurrentUser()

    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const supabase = createClient()
    const { data: categories, error } = await supabase
      .from('product_categories')
      .select(`
        *,
        products:products(id, name, base_price, is_active)
      `)
      .eq('merchant_id', user.merchant.id)
      .order('sort_order', { ascending: true })

    if (error) {
      console.error('Get product categories error:', error)
      return {
        success: false,
        error: '获取商品分类失败',
      }
    }

    return {
      success: true,
      data: categories as any,
    }
  } catch (error) {
    console.error('Get product categories error:', error)
    return {
      success: false,
      error: '获取商品分类失败',
    }
  }
}

// 创建商品分类
export async function createProductCategory(formData: ProductCategoryFormData): Promise<ProductActionResult> {
  try {
    const user = await getCurrentUser()

    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const categoryData = {
      merchant_id: user.merchant.id,
      name: formData.name,
      description: formData.description || null,
      parent_id: formData.parent_id || null,
      image: formData.image || null,
      sort_order: formData.sort_order || 0,
      is_active: formData.is_active !== false,
      meta_title: formData.meta_title || null,
      meta_description: formData.meta_description || null,
    }

    const supabase = createClient()
    const { data: category, error } = await supabase
      .from('product_categories')
      .insert(categoryData)
      .select()
      .single()

    if (error) {
      console.error('Create product category error:', error)
      return {
        success: false,
        error: '创建商品分类失败',
      }
    }

    revalidatePath('/products')

    return {
      success: true,
      data: category,
      message: '商品分类创建成功',
    }
  } catch (error) {
    console.error('Create product category error:', error)
    return {
      success: false,
      error: '创建商品分类失败，请稍后重试',
    }
  }
}

// 更新库存
export async function updateStock(formData: StockFormData): Promise<ProductActionResult> {
  try {
    const user = await getCurrentUser()

    if (!user || !user.merchant) {
      return {
        success: false,
        error: '用户未登录或未找到商家信息',
      }
    }

    const supabase = createClient()

    // 验证商品所有权
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('id, merchant_id, stock_quantity')
      .eq('id', formData.product_id)
      .eq('merchant_id', user.merchant.id)
      .single()

    if (productError || !product) {
      return {
        success: false,
        error: '无权操作该商品库存',
      }
    }

    let newQuantity: number
    const currentQuantity = product.stock_quantity || 0

    switch (formData.operation) {
      case 'add':
        newQuantity = currentQuantity + formData.quantity
        break
      case 'subtract':
        newQuantity = Math.max(0, currentQuantity - formData.quantity)
        break
      case 'set':
        newQuantity = formData.quantity
        break
      default:
        return {
          success: false,
          error: '无效的库存操作类型',
        }
    }

    // 更新商品库存
    const { data: updatedProduct, error: updateError } = await supabase
      .from('products')
      .update({
        stock_quantity: newQuantity,
        updated_at: new Date().toISOString()
      })
      .eq('id', formData.product_id)
      .select()
      .single()

    if (updateError) {
      console.error('Update stock error:', updateError)
      return {
        success: false,
        error: '更新库存失败',
      }
    }

    // 记录库存变动日志
    await supabase
      .from('stock_movements')
      .insert({
        product_id: formData.product_id,
        variant_id: formData.variant_id || null,
        operation: formData.operation,
        quantity: formData.quantity,
        previous_quantity: currentQuantity,
        new_quantity: newQuantity,
        reason: formData.reason || null,
        location: formData.location || null,
        batch_number: formData.batch_number || null,
        expiry_date: formData.expiry_date || null,
        notes: formData.notes || null,
        created_by: user.id,
      })

    revalidatePath('/products')
    revalidatePath(`/products/${formData.product_id}`)

    return {
      success: true,
      data: updatedProduct,
      message: '库存更新成功',
    }
  } catch (error) {
    console.error('Update stock error:', error)
    return {
      success: false,
      error: '更新库存失败，请稍后重试',
    }
  }
}
