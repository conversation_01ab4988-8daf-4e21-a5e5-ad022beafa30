/**
 * 性能监控工具
 * 监控应用性能指标和用户体验
 */

// 性能指标接口
export interface PerformanceMetrics {
  // Core Web Vitals
  fcp?: number // First Contentful Paint
  lcp?: number // Largest Contentful Paint
  fid?: number // First Input Delay
  cls?: number // Cumulative Layout Shift
  ttfb?: number // Time to First Byte
  
  // 自定义指标
  pageLoadTime?: number
  domContentLoaded?: number
  resourceLoadTime?: number
  apiResponseTime?: number
  
  // 用户体验指标
  interactionDelay?: number
  scrollResponsiveness?: number
  
  // 资源指标
  bundleSize?: number
  imageLoadTime?: number
  fontLoadTime?: number
  
  // 错误指标
  jsErrors?: number
  networkErrors?: number
  
  // 设备信息
  deviceType?: string
  connectionType?: string
  userAgent?: string
  
  // 页面信息
  url?: string
  referrer?: string
  timestamp?: number
}

// 性能监控类
export class PerformanceMonitor {
  private metrics: PerformanceMetrics = {}
  private observers: PerformanceObserver[] = []
  private startTime: number = Date.now()
  
  constructor() {
    if (typeof window !== 'undefined') {
      this.initializeMonitoring()
    }
  }
  
  private initializeMonitoring(): void {
    // 监控Core Web Vitals
    this.observeWebVitals()
    
    // 监控资源加载
    this.observeResourceTiming()
    
    // 监控长任务
    this.observeLongTasks()
    
    // 监控布局偏移
    this.observeLayoutShift()
    
    // 监控用户交互
    this.observeUserInteractions()
    
    // 页面卸载时发送数据
    this.setupBeaconSending()
  }
  
  private observeWebVitals(): void {
    // First Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const fcpEntry = entries.find(entry => entry.name === 'first-contentful-paint')
      if (fcpEntry) {
        this.metrics.fcp = fcpEntry.startTime
      }
    }).observe({ entryTypes: ['paint'] })
    
    // Largest Contentful Paint
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      this.metrics.lcp = lastEntry.startTime
    }).observe({ entryTypes: ['largest-contentful-paint'] })
    
    // First Input Delay
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      entries.forEach((entry: any) => {
        if (entry.processingStart && entry.startTime) {
          this.metrics.fid = entry.processingStart - entry.startTime
        }
      })
    }).observe({ entryTypes: ['first-input'] })
  }
  
  private observeResourceTiming(): void {
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      
      entries.forEach((entry: any) => {
        // 计算资源加载时间
        const loadTime = entry.responseEnd - entry.startTime
        
        if (entry.initiatorType === 'img') {
          this.metrics.imageLoadTime = (this.metrics.imageLoadTime || 0) + loadTime
        } else if (entry.initiatorType === 'script') {
          this.metrics.bundleSize = (this.metrics.bundleSize || 0) + (entry.transferSize || 0)
        }
      })
    }).observe({ entryTypes: ['resource'] })
  }
  
  private observeLongTasks(): void {
    if ('PerformanceObserver' in window && 'PerformanceLongTaskTiming' in window) {
      new PerformanceObserver((list) => {
        const entries = list.getEntries()
        entries.forEach((entry) => {
          // 记录长任务，可能影响用户体验
          console.warn('Long task detected:', entry.duration, 'ms')
        })
      }).observe({ entryTypes: ['longtask'] })
    }
  }
  
  private observeLayoutShift(): void {
    let clsValue = 0
    
    new PerformanceObserver((list) => {
      const entries = list.getEntries()
      
      entries.forEach((entry: any) => {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
          this.metrics.cls = clsValue
        }
      })
    }).observe({ entryTypes: ['layout-shift'] })
  }
  
  private observeUserInteractions(): void {
    let interactionCount = 0
    let totalDelay = 0
    
    const measureInteraction = (event: Event) => {
      const startTime = performance.now()
      
      requestAnimationFrame(() => {
        const delay = performance.now() - startTime
        totalDelay += delay
        interactionCount++
        this.metrics.interactionDelay = totalDelay / interactionCount
      })
    }
    
    ['click', 'keydown', 'touchstart'].forEach(eventType => {
      document.addEventListener(eventType, measureInteraction, { passive: true })
    })
  }
  
  private setupBeaconSending(): void {
    const sendMetrics = () => {
      this.collectFinalMetrics()
      this.sendMetricsToServer()
    }
    
    // 页面卸载时发送
    window.addEventListener('beforeunload', sendMetrics)
    
    // 页面隐藏时发送
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        sendMetrics()
      }
    })
    
    // 定期发送（每30秒）
    setInterval(() => {
      this.sendMetricsToServer()
    }, 30000)
  }
  
  private collectFinalMetrics(): void {
    // 收集页面加载时间
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (navigation) {
      this.metrics.pageLoadTime = navigation.loadEventEnd - navigation.fetchStart
      this.metrics.domContentLoaded = navigation.domContentLoadedEventEnd - navigation.fetchStart
      this.metrics.ttfb = navigation.responseStart - navigation.fetchStart
    }
    
    // 收集设备信息
    this.metrics.deviceType = this.getDeviceType()
    this.metrics.connectionType = this.getConnectionType()
    this.metrics.userAgent = navigator.userAgent
    
    // 收集页面信息
    this.metrics.url = window.location.href
    this.metrics.referrer = document.referrer
    this.metrics.timestamp = Date.now()
  }
  
  private getDeviceType(): string {
    const userAgent = navigator.userAgent
    if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      return 'tablet'
    }
    if (/mobile|iphone|ipod|android|blackberry|opera|mini|windows\sce|palm|smartphone|iemobile/i.test(userAgent)) {
      return 'mobile'
    }
    return 'desktop'
  }
  
  private getConnectionType(): string {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
    return connection?.effectiveType || 'unknown'
  }
  
  private sendMetricsToServer(): void {
    if (Object.keys(this.metrics).length === 0) return
    
    const data = {
      ...this.metrics,
      sessionId: this.getSessionId(),
      userId: this.getUserId(),
    }
    
    // 使用Beacon API发送数据（不阻塞页面卸载）
    if ('sendBeacon' in navigator) {
      navigator.sendBeacon('/api/performance/metrics', JSON.stringify(data))
    } else {
      // 降级方案
      fetch('/api/performance/metrics', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
        keepalive: true,
      }).catch(console.error)
    }
  }
  
  private getSessionId(): string {
    let sessionId = sessionStorage.getItem('sessionId')
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      sessionStorage.setItem('sessionId', sessionId)
    }
    return sessionId
  }
  
  private getUserId(): string {
    return localStorage.getItem('userId') || 'anonymous'
  }
  
  // 手动记录API响应时间
  public recordApiCall(url: string, duration: number, success: boolean): void {
    this.metrics.apiResponseTime = (this.metrics.apiResponseTime || 0) + duration
    
    if (!success) {
      this.metrics.networkErrors = (this.metrics.networkErrors || 0) + 1
    }
    
    // 发送API性能数据
    this.sendApiMetrics(url, duration, success)
  }
  
  private sendApiMetrics(url: string, duration: number, success: boolean): void {
    const data = {
      type: 'api_call',
      url,
      duration,
      success,
      timestamp: Date.now(),
      sessionId: this.getSessionId(),
    }
    
    fetch('/api/performance/api-metrics', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    }).catch(console.error)
  }
  
  // 手动记录错误
  public recordError(error: Error, context?: string): void {
    this.metrics.jsErrors = (this.metrics.jsErrors || 0) + 1
    
    const errorData = {
      type: 'js_error',
      message: error.message,
      stack: error.stack,
      context,
      url: window.location.href,
      timestamp: Date.now(),
      sessionId: this.getSessionId(),
      userAgent: navigator.userAgent,
    }
    
    fetch('/api/performance/errors', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(errorData),
    }).catch(console.error)
  }
  
  // 获取当前性能指标
  public getMetrics(): PerformanceMetrics {
    return { ...this.metrics }
  }
  
  // 清理资源
  public cleanup(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

// 全局性能监控实例
export const performanceMonitor = new PerformanceMonitor()

// 性能监控Hook
export function usePerformanceMonitoring() {
  return {
    recordApiCall: performanceMonitor.recordApiCall.bind(performanceMonitor),
    recordError: performanceMonitor.recordError.bind(performanceMonitor),
    getMetrics: performanceMonitor.getMetrics.bind(performanceMonitor),
  }
}

// API调用包装器，自动记录性能
export async function monitoredFetch(url: string, options?: RequestInit): Promise<Response> {
  const startTime = performance.now()
  
  try {
    const response = await fetch(url, options)
    const duration = performance.now() - startTime
    
    performanceMonitor.recordApiCall(url, duration, response.ok)
    
    return response
  } catch (error) {
    const duration = performance.now() - startTime
    performanceMonitor.recordApiCall(url, duration, false)
    throw error
  }
}

// 组件性能监控装饰器
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  return function MonitoredComponent(props: P) {
    const startTime = performance.now()
    
    React.useEffect(() => {
      const renderTime = performance.now() - startTime
      
      if (renderTime > 16) { // 超过一帧的时间
        console.warn(`Slow component render: ${componentName || Component.name} took ${renderTime.toFixed(2)}ms`)
      }
    })
    
    return React.createElement(Component, props)
  }
}
