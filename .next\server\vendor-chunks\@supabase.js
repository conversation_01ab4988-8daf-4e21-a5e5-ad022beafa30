"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@supabase";
exports.ids = ["vendor-chunks/@supabase"];
exports.modules = {

/***/ "(action-browser)/./node_modules/@supabase/ssr/dist/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* binding */ DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   combineChunks: () => (/* binding */ combineChunks),\n/* harmony export */   createBrowserClient: () => (/* binding */ createBrowserClient),\n/* harmony export */   createChunks: () => (/* binding */ createChunks),\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient),\n/* harmony export */   deleteChunks: () => (/* binding */ deleteChunks),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   parse: () => (/* reexport safe */ cookie__WEBPACK_IMPORTED_MODULE_1__.parse),\n/* harmony export */   serialize: () => (/* reexport safe */ cookie__WEBPACK_IMPORTED_MODULE_1__.serialize)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ramda */ \"(action-browser)/./node_modules/ramda/es/mergeDeepRight.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! cookie */ \"(action-browser)/./node_modules/cookie/index.js\");\n// src/createBrowserClient.ts\n\n\n\n// src/utils/helpers.ts\n\nfunction isBrowser() {\n  return typeof window !== \"undefined\" && typeof window.document !== \"undefined\";\n}\n\n// src/utils/constants.ts\nvar DEFAULT_COOKIE_OPTIONS = {\n  path: \"/\",\n  sameSite: \"lax\",\n  httpOnly: false,\n  maxAge: 60 * 60 * 24 * 365 * 1e3\n};\n\n// src/utils/chunker.ts\nvar MAX_CHUNK_SIZE = 3180;\nfunction createChunks(key, value, chunkSize) {\n  const resolvedChunkSize = chunkSize ?? MAX_CHUNK_SIZE;\n  let encodedValue = encodeURIComponent(value);\n  if (encodedValue.length <= resolvedChunkSize) {\n    return [{ name: key, value }];\n  }\n  const chunks = [];\n  while (encodedValue.length > 0) {\n    let encodedChunkHead = encodedValue.slice(0, resolvedChunkSize);\n    const lastEscapePos = encodedChunkHead.lastIndexOf(\"%\");\n    if (lastEscapePos > resolvedChunkSize - 3) {\n      encodedChunkHead = encodedChunkHead.slice(0, lastEscapePos);\n    }\n    let valueHead = \"\";\n    while (encodedChunkHead.length > 0) {\n      try {\n        valueHead = decodeURIComponent(encodedChunkHead);\n        break;\n      } catch (error) {\n        if (error instanceof URIError && encodedChunkHead.at(-3) === \"%\" && encodedChunkHead.length > 3) {\n          encodedChunkHead = encodedChunkHead.slice(0, encodedChunkHead.length - 3);\n        } else {\n          throw error;\n        }\n      }\n    }\n    chunks.push(valueHead);\n    encodedValue = encodedValue.slice(encodedChunkHead.length);\n  }\n  return chunks.map((value2, i) => ({ name: `${key}.${i}`, value: value2 }));\n}\nasync function combineChunks(key, retrieveChunk) {\n  const value = await retrieveChunk(key);\n  if (value) {\n    return value;\n  }\n  let values = [];\n  for (let i = 0; ; i++) {\n    const chunkName = `${key}.${i}`;\n    const chunk = await retrieveChunk(chunkName);\n    if (!chunk) {\n      break;\n    }\n    values.push(chunk);\n  }\n  if (values.length > 0) {\n    return values.join(\"\");\n  }\n}\nasync function deleteChunks(key, retrieveChunk, removeChunk) {\n  const value = await retrieveChunk(key);\n  if (value) {\n    await removeChunk(key);\n    return;\n  }\n  for (let i = 0; ; i++) {\n    const chunkName = `${key}.${i}`;\n    const chunk = await retrieveChunk(chunkName);\n    if (!chunk) {\n      break;\n    }\n    await removeChunk(chunkName);\n  }\n}\n\n// src/createBrowserClient.ts\n\nvar cachedBrowserClient;\nfunction createBrowserClient(supabaseUrl, supabaseKey, options) {\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      `Your project's URL and Key are required to create a Supabase client!\n\nCheck your Supabase project's API settings to find these values\n\nhttps://supabase.com/dashboard/project/_/settings/api`\n    );\n  }\n  let cookies = {};\n  let isSingleton = true;\n  let cookieOptions;\n  let userDefinedClientOptions;\n  if (options) {\n    ({ cookies, isSingleton = true, cookieOptions, ...userDefinedClientOptions } = options);\n  }\n  const cookieClientOptions = {\n    global: {\n      headers: {\n        \"X-Client-Info\": `${\"supabase-ssr\"}/${\"0.1.0\"}`\n      }\n    },\n    auth: {\n      flowType: \"pkce\",\n      autoRefreshToken: isBrowser(),\n      detectSessionInUrl: isBrowser(),\n      persistSession: true,\n      storage: {\n        // this client is used on the browser so cookies can be trusted\n        isServer: false,\n        getItem: async (key) => {\n          const chunkedCookie = await combineChunks(key, async (chunkName) => {\n            if (typeof cookies.get === \"function\") {\n              return await cookies.get(chunkName);\n            }\n            if (isBrowser()) {\n              const cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_1__.parse)(document.cookie);\n              return cookie[chunkName];\n            }\n          });\n          return chunkedCookie;\n        },\n        setItem: async (key, value) => {\n          const chunks = await createChunks(key, value);\n          await Promise.all(\n            chunks.map(async (chunk) => {\n              if (typeof cookies.set === \"function\") {\n                await cookies.set(chunk.name, chunk.value, {\n                  ...DEFAULT_COOKIE_OPTIONS,\n                  ...cookieOptions,\n                  maxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n                });\n              } else {\n                if (isBrowser()) {\n                  document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_1__.serialize)(chunk.name, chunk.value, {\n                    ...DEFAULT_COOKIE_OPTIONS,\n                    ...cookieOptions,\n                    maxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n                  });\n                }\n              }\n            })\n          );\n        },\n        removeItem: async (key) => {\n          if (typeof cookies.remove === \"function\" && typeof cookies.get !== \"function\") {\n            console.log(\n              \"Removing chunked cookie without a `get` method is not supported.\\n\\n\tWhen you call the `createBrowserClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\\n\\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client\"\n            );\n            return;\n          }\n          await deleteChunks(\n            key,\n            async (chunkName) => {\n              if (typeof cookies.get === \"function\") {\n                return await cookies.get(chunkName);\n              }\n              if (isBrowser()) {\n                const documentCookies = (0,cookie__WEBPACK_IMPORTED_MODULE_1__.parse)(document.cookie);\n                return documentCookies[chunkName];\n              }\n            },\n            async (chunkName) => {\n              if (typeof cookies.remove === \"function\") {\n                await cookies.remove(chunkName, {\n                  ...DEFAULT_COOKIE_OPTIONS,\n                  ...cookieOptions,\n                  maxAge: 0\n                });\n              } else {\n                if (isBrowser()) {\n                  document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_1__.serialize)(chunkName, \"\", {\n                    ...DEFAULT_COOKIE_OPTIONS,\n                    ...cookieOptions,\n                    maxAge: 0\n                  });\n                }\n              }\n            }\n          );\n        }\n      }\n    }\n  };\n  const clientOptions = (0,ramda__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\n    cookieClientOptions,\n    userDefinedClientOptions\n  );\n  if (isSingleton) {\n    const browser = isBrowser();\n    if (browser && cachedBrowserClient) {\n      return cachedBrowserClient;\n    }\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\n      supabaseUrl,\n      supabaseKey,\n      clientOptions\n    );\n    if (browser) {\n      cachedBrowserClient = client;\n    }\n    return client;\n  }\n  return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, clientOptions);\n}\n\n// src/createServerClient.ts\n\n\nfunction createServerClient(supabaseUrl, supabaseKey, options) {\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      `Your project's URL and Key are required to create a Supabase client!\n\nCheck your Supabase project's API settings to find these values\n\nhttps://supabase.com/dashboard/project/_/settings/api`\n    );\n  }\n  const { cookies, cookieOptions, ...userDefinedClientOptions } = options;\n  if (cookieOptions == null ? void 0 : cookieOptions.name) {\n    userDefinedClientOptions.auth = {\n      ...userDefinedClientOptions.auth,\n      storageKey: cookieOptions.name\n    };\n  }\n  const cookieClientOptions = {\n    global: {\n      headers: {\n        \"X-Client-Info\": `${\"supabase-ssr\"}/${\"0.1.0\"}`\n      }\n    },\n    auth: {\n      flowType: \"pkce\",\n      autoRefreshToken: isBrowser(),\n      detectSessionInUrl: isBrowser(),\n      persistSession: true,\n      storage: {\n        // to signal to the libraries that these cookies are coming from a server environment and their value should not be trusted\n        isServer: true,\n        getItem: async (key) => {\n          const chunkedCookie = await combineChunks(key, async (chunkName) => {\n            if (typeof cookies.get === \"function\") {\n              return await cookies.get(chunkName);\n            }\n          });\n          return chunkedCookie;\n        },\n        setItem: async (key, value) => {\n          const chunks = createChunks(key, value);\n          await Promise.all(\n            chunks.map(async (chunk) => {\n              if (typeof cookies.set === \"function\") {\n                await cookies.set(chunk.name, chunk.value, {\n                  ...DEFAULT_COOKIE_OPTIONS,\n                  ...cookieOptions,\n                  maxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n                });\n              }\n            })\n          );\n        },\n        removeItem: async (key) => {\n          if (typeof cookies.remove === \"function\" && typeof cookies.get !== \"function\") {\n            console.log(\n              \"Removing chunked cookie without a `get` method is not supported.\\n\\n\tWhen you call the `createServerClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\\n\\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client\"\n            );\n            return;\n          }\n          deleteChunks(\n            key,\n            async (chunkName) => {\n              if (typeof cookies.get === \"function\") {\n                return await cookies.get(chunkName);\n              }\n            },\n            async (chunkName) => {\n              if (typeof cookies.remove === \"function\") {\n                return await cookies.remove(chunkName, {\n                  ...DEFAULT_COOKIE_OPTIONS,\n                  ...cookieOptions,\n                  maxAge: 0\n                });\n              }\n            }\n          );\n        }\n      }\n    }\n  };\n  const clientOptions = (0,ramda__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\n    cookieClientOptions,\n    userDefinedClientOptions\n  );\n  return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, clientOptions);\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/@supabase/ssr/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@supabase/ssr/dist/index.mjs":
/*!***************************************************!*\
  !*** ./node_modules/@supabase/ssr/dist/index.mjs ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_COOKIE_OPTIONS: () => (/* binding */ DEFAULT_COOKIE_OPTIONS),\n/* harmony export */   combineChunks: () => (/* binding */ combineChunks),\n/* harmony export */   createBrowserClient: () => (/* binding */ createBrowserClient),\n/* harmony export */   createChunks: () => (/* binding */ createChunks),\n/* harmony export */   createServerClient: () => (/* binding */ createServerClient),\n/* harmony export */   deleteChunks: () => (/* binding */ deleteChunks),\n/* harmony export */   isBrowser: () => (/* binding */ isBrowser),\n/* harmony export */   parse: () => (/* reexport safe */ cookie__WEBPACK_IMPORTED_MODULE_1__.parse),\n/* harmony export */   serialize: () => (/* reexport safe */ cookie__WEBPACK_IMPORTED_MODULE_1__.serialize)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"@supabase/supabase-js\");\n/* harmony import */ var ramda__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ramda */ \"(ssr)/./node_modules/ramda/es/mergeDeepRight.js\");\n/* harmony import */ var cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! cookie */ \"(ssr)/./node_modules/cookie/index.js\");\n// src/createBrowserClient.ts\n\n\n\n// src/utils/helpers.ts\n\nfunction isBrowser() {\n  return typeof window !== \"undefined\" && typeof window.document !== \"undefined\";\n}\n\n// src/utils/constants.ts\nvar DEFAULT_COOKIE_OPTIONS = {\n  path: \"/\",\n  sameSite: \"lax\",\n  httpOnly: false,\n  maxAge: 60 * 60 * 24 * 365 * 1e3\n};\n\n// src/utils/chunker.ts\nvar MAX_CHUNK_SIZE = 3180;\nfunction createChunks(key, value, chunkSize) {\n  const resolvedChunkSize = chunkSize ?? MAX_CHUNK_SIZE;\n  let encodedValue = encodeURIComponent(value);\n  if (encodedValue.length <= resolvedChunkSize) {\n    return [{ name: key, value }];\n  }\n  const chunks = [];\n  while (encodedValue.length > 0) {\n    let encodedChunkHead = encodedValue.slice(0, resolvedChunkSize);\n    const lastEscapePos = encodedChunkHead.lastIndexOf(\"%\");\n    if (lastEscapePos > resolvedChunkSize - 3) {\n      encodedChunkHead = encodedChunkHead.slice(0, lastEscapePos);\n    }\n    let valueHead = \"\";\n    while (encodedChunkHead.length > 0) {\n      try {\n        valueHead = decodeURIComponent(encodedChunkHead);\n        break;\n      } catch (error) {\n        if (error instanceof URIError && encodedChunkHead.at(-3) === \"%\" && encodedChunkHead.length > 3) {\n          encodedChunkHead = encodedChunkHead.slice(0, encodedChunkHead.length - 3);\n        } else {\n          throw error;\n        }\n      }\n    }\n    chunks.push(valueHead);\n    encodedValue = encodedValue.slice(encodedChunkHead.length);\n  }\n  return chunks.map((value2, i) => ({ name: `${key}.${i}`, value: value2 }));\n}\nasync function combineChunks(key, retrieveChunk) {\n  const value = await retrieveChunk(key);\n  if (value) {\n    return value;\n  }\n  let values = [];\n  for (let i = 0; ; i++) {\n    const chunkName = `${key}.${i}`;\n    const chunk = await retrieveChunk(chunkName);\n    if (!chunk) {\n      break;\n    }\n    values.push(chunk);\n  }\n  if (values.length > 0) {\n    return values.join(\"\");\n  }\n}\nasync function deleteChunks(key, retrieveChunk, removeChunk) {\n  const value = await retrieveChunk(key);\n  if (value) {\n    await removeChunk(key);\n    return;\n  }\n  for (let i = 0; ; i++) {\n    const chunkName = `${key}.${i}`;\n    const chunk = await retrieveChunk(chunkName);\n    if (!chunk) {\n      break;\n    }\n    await removeChunk(chunkName);\n  }\n}\n\n// src/createBrowserClient.ts\n\nvar cachedBrowserClient;\nfunction createBrowserClient(supabaseUrl, supabaseKey, options) {\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      `Your project's URL and Key are required to create a Supabase client!\n\nCheck your Supabase project's API settings to find these values\n\nhttps://supabase.com/dashboard/project/_/settings/api`\n    );\n  }\n  let cookies = {};\n  let isSingleton = true;\n  let cookieOptions;\n  let userDefinedClientOptions;\n  if (options) {\n    ({ cookies, isSingleton = true, cookieOptions, ...userDefinedClientOptions } = options);\n  }\n  const cookieClientOptions = {\n    global: {\n      headers: {\n        \"X-Client-Info\": `${\"supabase-ssr\"}/${\"0.1.0\"}`\n      }\n    },\n    auth: {\n      flowType: \"pkce\",\n      autoRefreshToken: isBrowser(),\n      detectSessionInUrl: isBrowser(),\n      persistSession: true,\n      storage: {\n        // this client is used on the browser so cookies can be trusted\n        isServer: false,\n        getItem: async (key) => {\n          const chunkedCookie = await combineChunks(key, async (chunkName) => {\n            if (typeof cookies.get === \"function\") {\n              return await cookies.get(chunkName);\n            }\n            if (isBrowser()) {\n              const cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_1__.parse)(document.cookie);\n              return cookie[chunkName];\n            }\n          });\n          return chunkedCookie;\n        },\n        setItem: async (key, value) => {\n          const chunks = await createChunks(key, value);\n          await Promise.all(\n            chunks.map(async (chunk) => {\n              if (typeof cookies.set === \"function\") {\n                await cookies.set(chunk.name, chunk.value, {\n                  ...DEFAULT_COOKIE_OPTIONS,\n                  ...cookieOptions,\n                  maxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n                });\n              } else {\n                if (isBrowser()) {\n                  document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_1__.serialize)(chunk.name, chunk.value, {\n                    ...DEFAULT_COOKIE_OPTIONS,\n                    ...cookieOptions,\n                    maxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n                  });\n                }\n              }\n            })\n          );\n        },\n        removeItem: async (key) => {\n          if (typeof cookies.remove === \"function\" && typeof cookies.get !== \"function\") {\n            console.log(\n              \"Removing chunked cookie without a `get` method is not supported.\\n\\n\tWhen you call the `createBrowserClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\\n\\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client\"\n            );\n            return;\n          }\n          await deleteChunks(\n            key,\n            async (chunkName) => {\n              if (typeof cookies.get === \"function\") {\n                return await cookies.get(chunkName);\n              }\n              if (isBrowser()) {\n                const documentCookies = (0,cookie__WEBPACK_IMPORTED_MODULE_1__.parse)(document.cookie);\n                return documentCookies[chunkName];\n              }\n            },\n            async (chunkName) => {\n              if (typeof cookies.remove === \"function\") {\n                await cookies.remove(chunkName, {\n                  ...DEFAULT_COOKIE_OPTIONS,\n                  ...cookieOptions,\n                  maxAge: 0\n                });\n              } else {\n                if (isBrowser()) {\n                  document.cookie = (0,cookie__WEBPACK_IMPORTED_MODULE_1__.serialize)(chunkName, \"\", {\n                    ...DEFAULT_COOKIE_OPTIONS,\n                    ...cookieOptions,\n                    maxAge: 0\n                  });\n                }\n              }\n            }\n          );\n        }\n      }\n    }\n  };\n  const clientOptions = (0,ramda__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\n    cookieClientOptions,\n    userDefinedClientOptions\n  );\n  if (isSingleton) {\n    const browser = isBrowser();\n    if (browser && cachedBrowserClient) {\n      return cachedBrowserClient;\n    }\n    const client = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(\n      supabaseUrl,\n      supabaseKey,\n      clientOptions\n    );\n    if (browser) {\n      cachedBrowserClient = client;\n    }\n    return client;\n  }\n  return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, clientOptions);\n}\n\n// src/createServerClient.ts\n\n\nfunction createServerClient(supabaseUrl, supabaseKey, options) {\n  if (!supabaseUrl || !supabaseKey) {\n    throw new Error(\n      `Your project's URL and Key are required to create a Supabase client!\n\nCheck your Supabase project's API settings to find these values\n\nhttps://supabase.com/dashboard/project/_/settings/api`\n    );\n  }\n  const { cookies, cookieOptions, ...userDefinedClientOptions } = options;\n  if (cookieOptions == null ? void 0 : cookieOptions.name) {\n    userDefinedClientOptions.auth = {\n      ...userDefinedClientOptions.auth,\n      storageKey: cookieOptions.name\n    };\n  }\n  const cookieClientOptions = {\n    global: {\n      headers: {\n        \"X-Client-Info\": `${\"supabase-ssr\"}/${\"0.1.0\"}`\n      }\n    },\n    auth: {\n      flowType: \"pkce\",\n      autoRefreshToken: isBrowser(),\n      detectSessionInUrl: isBrowser(),\n      persistSession: true,\n      storage: {\n        // to signal to the libraries that these cookies are coming from a server environment and their value should not be trusted\n        isServer: true,\n        getItem: async (key) => {\n          const chunkedCookie = await combineChunks(key, async (chunkName) => {\n            if (typeof cookies.get === \"function\") {\n              return await cookies.get(chunkName);\n            }\n          });\n          return chunkedCookie;\n        },\n        setItem: async (key, value) => {\n          const chunks = createChunks(key, value);\n          await Promise.all(\n            chunks.map(async (chunk) => {\n              if (typeof cookies.set === \"function\") {\n                await cookies.set(chunk.name, chunk.value, {\n                  ...DEFAULT_COOKIE_OPTIONS,\n                  ...cookieOptions,\n                  maxAge: DEFAULT_COOKIE_OPTIONS.maxAge\n                });\n              }\n            })\n          );\n        },\n        removeItem: async (key) => {\n          if (typeof cookies.remove === \"function\" && typeof cookies.get !== \"function\") {\n            console.log(\n              \"Removing chunked cookie without a `get` method is not supported.\\n\\n\tWhen you call the `createServerClient` function from the `@supabase/ssr` package, make sure you declare both a `get` and `remove` method on the `cookies` object.\\n\\nhttps://supabase.com/docs/guides/auth/server-side/creating-a-client\"\n            );\n            return;\n          }\n          deleteChunks(\n            key,\n            async (chunkName) => {\n              if (typeof cookies.get === \"function\") {\n                return await cookies.get(chunkName);\n              }\n            },\n            async (chunkName) => {\n              if (typeof cookies.remove === \"function\") {\n                return await cookies.remove(chunkName, {\n                  ...DEFAULT_COOKIE_OPTIONS,\n                  ...cookieOptions,\n                  maxAge: 0\n                });\n              }\n            }\n          );\n        }\n      }\n    }\n  };\n  const clientOptions = (0,ramda__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(\n    cookieClientOptions,\n    userDefinedClientOptions\n  );\n  return (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, clientOptions);\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHN1cGFiYXNlL3Nzci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNxRDtBQUNkOztBQUV2QztBQUMwQztBQUMxQztBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYyxrQkFBa0I7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyxTQUFTLElBQUksR0FBRyxFQUFFLGtCQUFrQjtBQUMxRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQjtBQUNwQix5QkFBeUIsSUFBSSxHQUFHLEVBQUU7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0I7QUFDcEIseUJBQXlCLElBQUksR0FBRyxFQUFFO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ2tFO0FBQ2xFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU8sMEVBQTBFO0FBQ2pGO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLGVBQWUsR0FBRyxRQUFRO0FBQ3REO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIsNkNBQU07QUFDbkM7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsZ0JBQWdCO0FBQ2hCO0FBQ0Esb0NBQW9DLGlEQUFVO0FBQzlDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsNkNBQU07QUFDOUM7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakIsZ0JBQWdCO0FBQ2hCO0FBQ0Esb0NBQW9DLGlEQUFVO0FBQzlDO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQjtBQUNuQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGlEQUFjO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtQkFBbUIsbUVBQVk7QUFDL0I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyxtRUFBWTtBQUNyQjs7QUFFQTtBQUNzRTtBQUNaO0FBQzFEO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFVBQVUsc0RBQXNEO0FBQ2hFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixlQUFlLEdBQUcsUUFBUTtBQUN0RDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxhQUFhO0FBQ2I7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGlEQUFlO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBLFNBQVMsbUVBQWE7QUFDdEI7QUFXRTtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFkbWluXFxEZXNrdG9wXFwwMDfov57plIHphZLlupdcXG5vZGVfbW9kdWxlc1xcQHN1cGFiYXNlXFxzc3JcXGRpc3RcXGluZGV4Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBzcmMvY3JlYXRlQnJvd3NlckNsaWVudC50c1xuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSBcIkBzdXBhYmFzZS9zdXBhYmFzZS1qc1wiO1xuaW1wb3J0IHsgbWVyZ2VEZWVwUmlnaHQgfSBmcm9tIFwicmFtZGFcIjtcblxuLy8gc3JjL3V0aWxzL2hlbHBlcnMudHNcbmltcG9ydCB7IHBhcnNlLCBzZXJpYWxpemUgfSBmcm9tIFwiY29va2llXCI7XG5mdW5jdGlvbiBpc0Jyb3dzZXIoKSB7XG4gIHJldHVybiB0eXBlb2Ygd2luZG93ICE9PSBcInVuZGVmaW5lZFwiICYmIHR5cGVvZiB3aW5kb3cuZG9jdW1lbnQgIT09IFwidW5kZWZpbmVkXCI7XG59XG5cbi8vIHNyYy91dGlscy9jb25zdGFudHMudHNcbnZhciBERUZBVUxUX0NPT0tJRV9PUFRJT05TID0ge1xuICBwYXRoOiBcIi9cIixcbiAgc2FtZVNpdGU6IFwibGF4XCIsXG4gIGh0dHBPbmx5OiBmYWxzZSxcbiAgbWF4QWdlOiA2MCAqIDYwICogMjQgKiAzNjUgKiAxZTNcbn07XG5cbi8vIHNyYy91dGlscy9jaHVua2VyLnRzXG52YXIgTUFYX0NIVU5LX1NJWkUgPSAzMTgwO1xuZnVuY3Rpb24gY3JlYXRlQ2h1bmtzKGtleSwgdmFsdWUsIGNodW5rU2l6ZSkge1xuICBjb25zdCByZXNvbHZlZENodW5rU2l6ZSA9IGNodW5rU2l6ZSA/PyBNQVhfQ0hVTktfU0laRTtcbiAgbGV0IGVuY29kZWRWYWx1ZSA9IGVuY29kZVVSSUNvbXBvbmVudCh2YWx1ZSk7XG4gIGlmIChlbmNvZGVkVmFsdWUubGVuZ3RoIDw9IHJlc29sdmVkQ2h1bmtTaXplKSB7XG4gICAgcmV0dXJuIFt7IG5hbWU6IGtleSwgdmFsdWUgfV07XG4gIH1cbiAgY29uc3QgY2h1bmtzID0gW107XG4gIHdoaWxlIChlbmNvZGVkVmFsdWUubGVuZ3RoID4gMCkge1xuICAgIGxldCBlbmNvZGVkQ2h1bmtIZWFkID0gZW5jb2RlZFZhbHVlLnNsaWNlKDAsIHJlc29sdmVkQ2h1bmtTaXplKTtcbiAgICBjb25zdCBsYXN0RXNjYXBlUG9zID0gZW5jb2RlZENodW5rSGVhZC5sYXN0SW5kZXhPZihcIiVcIik7XG4gICAgaWYgKGxhc3RFc2NhcGVQb3MgPiByZXNvbHZlZENodW5rU2l6ZSAtIDMpIHtcbiAgICAgIGVuY29kZWRDaHVua0hlYWQgPSBlbmNvZGVkQ2h1bmtIZWFkLnNsaWNlKDAsIGxhc3RFc2NhcGVQb3MpO1xuICAgIH1cbiAgICBsZXQgdmFsdWVIZWFkID0gXCJcIjtcbiAgICB3aGlsZSAoZW5jb2RlZENodW5rSGVhZC5sZW5ndGggPiAwKSB7XG4gICAgICB0cnkge1xuICAgICAgICB2YWx1ZUhlYWQgPSBkZWNvZGVVUklDb21wb25lbnQoZW5jb2RlZENodW5rSGVhZCk7XG4gICAgICAgIGJyZWFrO1xuICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgVVJJRXJyb3IgJiYgZW5jb2RlZENodW5rSGVhZC5hdCgtMykgPT09IFwiJVwiICYmIGVuY29kZWRDaHVua0hlYWQubGVuZ3RoID4gMykge1xuICAgICAgICAgIGVuY29kZWRDaHVua0hlYWQgPSBlbmNvZGVkQ2h1bmtIZWFkLnNsaWNlKDAsIGVuY29kZWRDaHVua0hlYWQubGVuZ3RoIC0gMyk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgdGhyb3cgZXJyb3I7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgY2h1bmtzLnB1c2godmFsdWVIZWFkKTtcbiAgICBlbmNvZGVkVmFsdWUgPSBlbmNvZGVkVmFsdWUuc2xpY2UoZW5jb2RlZENodW5rSGVhZC5sZW5ndGgpO1xuICB9XG4gIHJldHVybiBjaHVua3MubWFwKCh2YWx1ZTIsIGkpID0+ICh7IG5hbWU6IGAke2tleX0uJHtpfWAsIHZhbHVlOiB2YWx1ZTIgfSkpO1xufVxuYXN5bmMgZnVuY3Rpb24gY29tYmluZUNodW5rcyhrZXksIHJldHJpZXZlQ2h1bmspIHtcbiAgY29uc3QgdmFsdWUgPSBhd2FpdCByZXRyaWV2ZUNodW5rKGtleSk7XG4gIGlmICh2YWx1ZSkge1xuICAgIHJldHVybiB2YWx1ZTtcbiAgfVxuICBsZXQgdmFsdWVzID0gW107XG4gIGZvciAobGV0IGkgPSAwOyA7IGkrKykge1xuICAgIGNvbnN0IGNodW5rTmFtZSA9IGAke2tleX0uJHtpfWA7XG4gICAgY29uc3QgY2h1bmsgPSBhd2FpdCByZXRyaWV2ZUNodW5rKGNodW5rTmFtZSk7XG4gICAgaWYgKCFjaHVuaykge1xuICAgICAgYnJlYWs7XG4gICAgfVxuICAgIHZhbHVlcy5wdXNoKGNodW5rKTtcbiAgfVxuICBpZiAodmFsdWVzLmxlbmd0aCA+IDApIHtcbiAgICByZXR1cm4gdmFsdWVzLmpvaW4oXCJcIik7XG4gIH1cbn1cbmFzeW5jIGZ1bmN0aW9uIGRlbGV0ZUNodW5rcyhrZXksIHJldHJpZXZlQ2h1bmssIHJlbW92ZUNodW5rKSB7XG4gIGNvbnN0IHZhbHVlID0gYXdhaXQgcmV0cmlldmVDaHVuayhrZXkpO1xuICBpZiAodmFsdWUpIHtcbiAgICBhd2FpdCByZW1vdmVDaHVuayhrZXkpO1xuICAgIHJldHVybjtcbiAgfVxuICBmb3IgKGxldCBpID0gMDsgOyBpKyspIHtcbiAgICBjb25zdCBjaHVua05hbWUgPSBgJHtrZXl9LiR7aX1gO1xuICAgIGNvbnN0IGNodW5rID0gYXdhaXQgcmV0cmlldmVDaHVuayhjaHVua05hbWUpO1xuICAgIGlmICghY2h1bmspIHtcbiAgICAgIGJyZWFrO1xuICAgIH1cbiAgICBhd2FpdCByZW1vdmVDaHVuayhjaHVua05hbWUpO1xuICB9XG59XG5cbi8vIHNyYy9jcmVhdGVCcm93c2VyQ2xpZW50LnRzXG5pbXBvcnQgeyBwYXJzZSBhcyBwYXJzZTIsIHNlcmlhbGl6ZSBhcyBzZXJpYWxpemUyIH0gZnJvbSBcImNvb2tpZVwiO1xudmFyIGNhY2hlZEJyb3dzZXJDbGllbnQ7XG5mdW5jdGlvbiBjcmVhdGVCcm93c2VyQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwgb3B0aW9ucykge1xuICBpZiAoIXN1cGFiYXNlVXJsIHx8ICFzdXBhYmFzZUtleSkge1xuICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgIGBZb3VyIHByb2plY3QncyBVUkwgYW5kIEtleSBhcmUgcmVxdWlyZWQgdG8gY3JlYXRlIGEgU3VwYWJhc2UgY2xpZW50IVxuXG5DaGVjayB5b3VyIFN1cGFiYXNlIHByb2plY3QncyBBUEkgc2V0dGluZ3MgdG8gZmluZCB0aGVzZSB2YWx1ZXNcblxuaHR0cHM6Ly9zdXBhYmFzZS5jb20vZGFzaGJvYXJkL3Byb2plY3QvXy9zZXR0aW5ncy9hcGlgXG4gICAgKTtcbiAgfVxuICBsZXQgY29va2llcyA9IHt9O1xuICBsZXQgaXNTaW5nbGV0b24gPSB0cnVlO1xuICBsZXQgY29va2llT3B0aW9ucztcbiAgbGV0IHVzZXJEZWZpbmVkQ2xpZW50T3B0aW9ucztcbiAgaWYgKG9wdGlvbnMpIHtcbiAgICAoeyBjb29raWVzLCBpc1NpbmdsZXRvbiA9IHRydWUsIGNvb2tpZU9wdGlvbnMsIC4uLnVzZXJEZWZpbmVkQ2xpZW50T3B0aW9ucyB9ID0gb3B0aW9ucyk7XG4gIH1cbiAgY29uc3QgY29va2llQ2xpZW50T3B0aW9ucyA9IHtcbiAgICBnbG9iYWw6IHtcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgXCJYLUNsaWVudC1JbmZvXCI6IGAke1wic3VwYWJhc2Utc3NyXCJ9LyR7XCIwLjEuMFwifWBcbiAgICAgIH1cbiAgICB9LFxuICAgIGF1dGg6IHtcbiAgICAgIGZsb3dUeXBlOiBcInBrY2VcIixcbiAgICAgIGF1dG9SZWZyZXNoVG9rZW46IGlzQnJvd3NlcigpLFxuICAgICAgZGV0ZWN0U2Vzc2lvbkluVXJsOiBpc0Jyb3dzZXIoKSxcbiAgICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLFxuICAgICAgc3RvcmFnZToge1xuICAgICAgICAvLyB0aGlzIGNsaWVudCBpcyB1c2VkIG9uIHRoZSBicm93c2VyIHNvIGNvb2tpZXMgY2FuIGJlIHRydXN0ZWRcbiAgICAgICAgaXNTZXJ2ZXI6IGZhbHNlLFxuICAgICAgICBnZXRJdGVtOiBhc3luYyAoa2V5KSA9PiB7XG4gICAgICAgICAgY29uc3QgY2h1bmtlZENvb2tpZSA9IGF3YWl0IGNvbWJpbmVDaHVua3Moa2V5LCBhc3luYyAoY2h1bmtOYW1lKSA9PiB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIGNvb2tpZXMuZ2V0ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgICAgcmV0dXJuIGF3YWl0IGNvb2tpZXMuZ2V0KGNodW5rTmFtZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBpZiAoaXNCcm93c2VyKCkpIHtcbiAgICAgICAgICAgICAgY29uc3QgY29va2llID0gcGFyc2UyKGRvY3VtZW50LmNvb2tpZSk7XG4gICAgICAgICAgICAgIHJldHVybiBjb29raWVbY2h1bmtOYW1lXTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgICByZXR1cm4gY2h1bmtlZENvb2tpZTtcbiAgICAgICAgfSxcbiAgICAgICAgc2V0SXRlbTogYXN5bmMgKGtleSwgdmFsdWUpID0+IHtcbiAgICAgICAgICBjb25zdCBjaHVua3MgPSBhd2FpdCBjcmVhdGVDaHVua3Moa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoXG4gICAgICAgICAgICBjaHVua3MubWFwKGFzeW5jIChjaHVuaykgPT4ge1xuICAgICAgICAgICAgICBpZiAodHlwZW9mIGNvb2tpZXMuc2V0ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgICAgICBhd2FpdCBjb29raWVzLnNldChjaHVuay5uYW1lLCBjaHVuay52YWx1ZSwge1xuICAgICAgICAgICAgICAgICAgLi4uREVGQVVMVF9DT09LSUVfT1BUSU9OUyxcbiAgICAgICAgICAgICAgICAgIC4uLmNvb2tpZU9wdGlvbnMsXG4gICAgICAgICAgICAgICAgICBtYXhBZ2U6IERFRkFVTFRfQ09PS0lFX09QVElPTlMubWF4QWdlXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgaWYgKGlzQnJvd3NlcigpKSB7XG4gICAgICAgICAgICAgICAgICBkb2N1bWVudC5jb29raWUgPSBzZXJpYWxpemUyKGNodW5rLm5hbWUsIGNodW5rLnZhbHVlLCB7XG4gICAgICAgICAgICAgICAgICAgIC4uLkRFRkFVTFRfQ09PS0lFX09QVElPTlMsXG4gICAgICAgICAgICAgICAgICAgIC4uLmNvb2tpZU9wdGlvbnMsXG4gICAgICAgICAgICAgICAgICAgIG1heEFnZTogREVGQVVMVF9DT09LSUVfT1BUSU9OUy5tYXhBZ2VcbiAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSlcbiAgICAgICAgICApO1xuICAgICAgICB9LFxuICAgICAgICByZW1vdmVJdGVtOiBhc3luYyAoa2V5KSA9PiB7XG4gICAgICAgICAgaWYgKHR5cGVvZiBjb29raWVzLnJlbW92ZSA9PT0gXCJmdW5jdGlvblwiICYmIHR5cGVvZiBjb29raWVzLmdldCAhPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICBjb25zb2xlLmxvZyhcbiAgICAgICAgICAgICAgXCJSZW1vdmluZyBjaHVua2VkIGNvb2tpZSB3aXRob3V0IGEgYGdldGAgbWV0aG9kIGlzIG5vdCBzdXBwb3J0ZWQuXFxuXFxuXHRXaGVuIHlvdSBjYWxsIHRoZSBgY3JlYXRlQnJvd3NlckNsaWVudGAgZnVuY3Rpb24gZnJvbSB0aGUgYEBzdXBhYmFzZS9zc3JgIHBhY2thZ2UsIG1ha2Ugc3VyZSB5b3UgZGVjbGFyZSBib3RoIGEgYGdldGAgYW5kIGByZW1vdmVgIG1ldGhvZCBvbiB0aGUgYGNvb2tpZXNgIG9iamVjdC5cXG5cXG5odHRwczovL3N1cGFiYXNlLmNvbS9kb2NzL2d1aWRlcy9hdXRoL3NlcnZlci1zaWRlL2NyZWF0aW5nLWEtY2xpZW50XCJcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICAgIGF3YWl0IGRlbGV0ZUNodW5rcyhcbiAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgIGFzeW5jIChjaHVua05hbWUpID0+IHtcbiAgICAgICAgICAgICAgaWYgKHR5cGVvZiBjb29raWVzLmdldCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGF3YWl0IGNvb2tpZXMuZ2V0KGNodW5rTmFtZSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgaWYgKGlzQnJvd3NlcigpKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZG9jdW1lbnRDb29raWVzID0gcGFyc2UyKGRvY3VtZW50LmNvb2tpZSk7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGRvY3VtZW50Q29va2llc1tjaHVua05hbWVdO1xuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgYXN5bmMgKGNodW5rTmFtZSkgPT4ge1xuICAgICAgICAgICAgICBpZiAodHlwZW9mIGNvb2tpZXMucmVtb3ZlID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgICAgICBhd2FpdCBjb29raWVzLnJlbW92ZShjaHVua05hbWUsIHtcbiAgICAgICAgICAgICAgICAgIC4uLkRFRkFVTFRfQ09PS0lFX09QVElPTlMsXG4gICAgICAgICAgICAgICAgICAuLi5jb29raWVPcHRpb25zLFxuICAgICAgICAgICAgICAgICAgbWF4QWdlOiAwXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgaWYgKGlzQnJvd3NlcigpKSB7XG4gICAgICAgICAgICAgICAgICBkb2N1bWVudC5jb29raWUgPSBzZXJpYWxpemUyKGNodW5rTmFtZSwgXCJcIiwge1xuICAgICAgICAgICAgICAgICAgICAuLi5ERUZBVUxUX0NPT0tJRV9PUFRJT05TLFxuICAgICAgICAgICAgICAgICAgICAuLi5jb29raWVPcHRpb25zLFxuICAgICAgICAgICAgICAgICAgICBtYXhBZ2U6IDBcbiAgICAgICAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gIH07XG4gIGNvbnN0IGNsaWVudE9wdGlvbnMgPSBtZXJnZURlZXBSaWdodChcbiAgICBjb29raWVDbGllbnRPcHRpb25zLFxuICAgIHVzZXJEZWZpbmVkQ2xpZW50T3B0aW9uc1xuICApO1xuICBpZiAoaXNTaW5nbGV0b24pIHtcbiAgICBjb25zdCBicm93c2VyID0gaXNCcm93c2VyKCk7XG4gICAgaWYgKGJyb3dzZXIgJiYgY2FjaGVkQnJvd3NlckNsaWVudCkge1xuICAgICAgcmV0dXJuIGNhY2hlZEJyb3dzZXJDbGllbnQ7XG4gICAgfVxuICAgIGNvbnN0IGNsaWVudCA9IGNyZWF0ZUNsaWVudChcbiAgICAgIHN1cGFiYXNlVXJsLFxuICAgICAgc3VwYWJhc2VLZXksXG4gICAgICBjbGllbnRPcHRpb25zXG4gICAgKTtcbiAgICBpZiAoYnJvd3Nlcikge1xuICAgICAgY2FjaGVkQnJvd3NlckNsaWVudCA9IGNsaWVudDtcbiAgICB9XG4gICAgcmV0dXJuIGNsaWVudDtcbiAgfVxuICByZXR1cm4gY3JlYXRlQ2xpZW50KHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwgY2xpZW50T3B0aW9ucyk7XG59XG5cbi8vIHNyYy9jcmVhdGVTZXJ2ZXJDbGllbnQudHNcbmltcG9ydCB7IGNyZWF0ZUNsaWVudCBhcyBjcmVhdGVDbGllbnQyIH0gZnJvbSBcIkBzdXBhYmFzZS9zdXBhYmFzZS1qc1wiO1xuaW1wb3J0IHsgbWVyZ2VEZWVwUmlnaHQgYXMgbWVyZ2VEZWVwUmlnaHQyIH0gZnJvbSBcInJhbWRhXCI7XG5mdW5jdGlvbiBjcmVhdGVTZXJ2ZXJDbGllbnQoc3VwYWJhc2VVcmwsIHN1cGFiYXNlS2V5LCBvcHRpb25zKSB7XG4gIGlmICghc3VwYWJhc2VVcmwgfHwgIXN1cGFiYXNlS2V5KSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgYFlvdXIgcHJvamVjdCdzIFVSTCBhbmQgS2V5IGFyZSByZXF1aXJlZCB0byBjcmVhdGUgYSBTdXBhYmFzZSBjbGllbnQhXG5cbkNoZWNrIHlvdXIgU3VwYWJhc2UgcHJvamVjdCdzIEFQSSBzZXR0aW5ncyB0byBmaW5kIHRoZXNlIHZhbHVlc1xuXG5odHRwczovL3N1cGFiYXNlLmNvbS9kYXNoYm9hcmQvcHJvamVjdC9fL3NldHRpbmdzL2FwaWBcbiAgICApO1xuICB9XG4gIGNvbnN0IHsgY29va2llcywgY29va2llT3B0aW9ucywgLi4udXNlckRlZmluZWRDbGllbnRPcHRpb25zIH0gPSBvcHRpb25zO1xuICBpZiAoY29va2llT3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogY29va2llT3B0aW9ucy5uYW1lKSB7XG4gICAgdXNlckRlZmluZWRDbGllbnRPcHRpb25zLmF1dGggPSB7XG4gICAgICAuLi51c2VyRGVmaW5lZENsaWVudE9wdGlvbnMuYXV0aCxcbiAgICAgIHN0b3JhZ2VLZXk6IGNvb2tpZU9wdGlvbnMubmFtZVxuICAgIH07XG4gIH1cbiAgY29uc3QgY29va2llQ2xpZW50T3B0aW9ucyA9IHtcbiAgICBnbG9iYWw6IHtcbiAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgXCJYLUNsaWVudC1JbmZvXCI6IGAke1wic3VwYWJhc2Utc3NyXCJ9LyR7XCIwLjEuMFwifWBcbiAgICAgIH1cbiAgICB9LFxuICAgIGF1dGg6IHtcbiAgICAgIGZsb3dUeXBlOiBcInBrY2VcIixcbiAgICAgIGF1dG9SZWZyZXNoVG9rZW46IGlzQnJvd3NlcigpLFxuICAgICAgZGV0ZWN0U2Vzc2lvbkluVXJsOiBpc0Jyb3dzZXIoKSxcbiAgICAgIHBlcnNpc3RTZXNzaW9uOiB0cnVlLFxuICAgICAgc3RvcmFnZToge1xuICAgICAgICAvLyB0byBzaWduYWwgdG8gdGhlIGxpYnJhcmllcyB0aGF0IHRoZXNlIGNvb2tpZXMgYXJlIGNvbWluZyBmcm9tIGEgc2VydmVyIGVudmlyb25tZW50IGFuZCB0aGVpciB2YWx1ZSBzaG91bGQgbm90IGJlIHRydXN0ZWRcbiAgICAgICAgaXNTZXJ2ZXI6IHRydWUsXG4gICAgICAgIGdldEl0ZW06IGFzeW5jIChrZXkpID0+IHtcbiAgICAgICAgICBjb25zdCBjaHVua2VkQ29va2llID0gYXdhaXQgY29tYmluZUNodW5rcyhrZXksIGFzeW5jIChjaHVua05hbWUpID0+IHtcbiAgICAgICAgICAgIGlmICh0eXBlb2YgY29va2llcy5nZXQgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgICByZXR1cm4gYXdhaXQgY29va2llcy5nZXQoY2h1bmtOYW1lKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9KTtcbiAgICAgICAgICByZXR1cm4gY2h1bmtlZENvb2tpZTtcbiAgICAgICAgfSxcbiAgICAgICAgc2V0SXRlbTogYXN5bmMgKGtleSwgdmFsdWUpID0+IHtcbiAgICAgICAgICBjb25zdCBjaHVua3MgPSBjcmVhdGVDaHVua3Moa2V5LCB2YWx1ZSk7XG4gICAgICAgICAgYXdhaXQgUHJvbWlzZS5hbGwoXG4gICAgICAgICAgICBjaHVua3MubWFwKGFzeW5jIChjaHVuaykgPT4ge1xuICAgICAgICAgICAgICBpZiAodHlwZW9mIGNvb2tpZXMuc2V0ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgICAgICAgICBhd2FpdCBjb29raWVzLnNldChjaHVuay5uYW1lLCBjaHVuay52YWx1ZSwge1xuICAgICAgICAgICAgICAgICAgLi4uREVGQVVMVF9DT09LSUVfT1BUSU9OUyxcbiAgICAgICAgICAgICAgICAgIC4uLmNvb2tpZU9wdGlvbnMsXG4gICAgICAgICAgICAgICAgICBtYXhBZ2U6IERFRkFVTFRfQ09PS0lFX09QVElPTlMubWF4QWdlXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pXG4gICAgICAgICAgKTtcbiAgICAgICAgfSxcbiAgICAgICAgcmVtb3ZlSXRlbTogYXN5bmMgKGtleSkgPT4ge1xuICAgICAgICAgIGlmICh0eXBlb2YgY29va2llcy5yZW1vdmUgPT09IFwiZnVuY3Rpb25cIiAmJiB0eXBlb2YgY29va2llcy5nZXQgIT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICAgIFwiUmVtb3ZpbmcgY2h1bmtlZCBjb29raWUgd2l0aG91dCBhIGBnZXRgIG1ldGhvZCBpcyBub3Qgc3VwcG9ydGVkLlxcblxcblx0V2hlbiB5b3UgY2FsbCB0aGUgYGNyZWF0ZVNlcnZlckNsaWVudGAgZnVuY3Rpb24gZnJvbSB0aGUgYEBzdXBhYmFzZS9zc3JgIHBhY2thZ2UsIG1ha2Ugc3VyZSB5b3UgZGVjbGFyZSBib3RoIGEgYGdldGAgYW5kIGByZW1vdmVgIG1ldGhvZCBvbiB0aGUgYGNvb2tpZXNgIG9iamVjdC5cXG5cXG5odHRwczovL3N1cGFiYXNlLmNvbS9kb2NzL2d1aWRlcy9hdXRoL3NlcnZlci1zaWRlL2NyZWF0aW5nLWEtY2xpZW50XCJcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgfVxuICAgICAgICAgIGRlbGV0ZUNodW5rcyhcbiAgICAgICAgICAgIGtleSxcbiAgICAgICAgICAgIGFzeW5jIChjaHVua05hbWUpID0+IHtcbiAgICAgICAgICAgICAgaWYgKHR5cGVvZiBjb29raWVzLmdldCA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICAgICAgcmV0dXJuIGF3YWl0IGNvb2tpZXMuZ2V0KGNodW5rTmFtZSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBhc3luYyAoY2h1bmtOYW1lKSA9PiB7XG4gICAgICAgICAgICAgIGlmICh0eXBlb2YgY29va2llcy5yZW1vdmUgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgICAgIHJldHVybiBhd2FpdCBjb29raWVzLnJlbW92ZShjaHVua05hbWUsIHtcbiAgICAgICAgICAgICAgICAgIC4uLkRFRkFVTFRfQ09PS0lFX09QVElPTlMsXG4gICAgICAgICAgICAgICAgICAuLi5jb29raWVPcHRpb25zLFxuICAgICAgICAgICAgICAgICAgbWF4QWdlOiAwXG4gICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9O1xuICBjb25zdCBjbGllbnRPcHRpb25zID0gbWVyZ2VEZWVwUmlnaHQyKFxuICAgIGNvb2tpZUNsaWVudE9wdGlvbnMsXG4gICAgdXNlckRlZmluZWRDbGllbnRPcHRpb25zXG4gICk7XG4gIHJldHVybiBjcmVhdGVDbGllbnQyKHN1cGFiYXNlVXJsLCBzdXBhYmFzZUtleSwgY2xpZW50T3B0aW9ucyk7XG59XG5leHBvcnQge1xuICBERUZBVUxUX0NPT0tJRV9PUFRJT05TLFxuICBjb21iaW5lQ2h1bmtzLFxuICBjcmVhdGVCcm93c2VyQ2xpZW50LFxuICBjcmVhdGVDaHVua3MsXG4gIGNyZWF0ZVNlcnZlckNsaWVudCxcbiAgZGVsZXRlQ2h1bmtzLFxuICBpc0Jyb3dzZXIsXG4gIHBhcnNlLFxuICBzZXJpYWxpemVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@supabase/ssr/dist/index.mjs\n");

/***/ })

};
;