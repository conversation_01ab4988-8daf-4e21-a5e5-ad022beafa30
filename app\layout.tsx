import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { AuthProvider } from "@/lib/auth/context";
import { ErrorBoundary } from '@/components/error-boundary';
import { Toaster } from '@/components/ui/toaster';
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "柒零支付连锁 - 商家服务平台",
  description: "基于Next.js 15 + Supabase的现代全栈商家管理平台，支持酒店、餐饮、商品管理和多支付方式集成",
  keywords: ["商家管理", "酒店管理", "餐饮管理", "支付系统", "柒零支付"],
  manifest: '/manifest.json',
  themeColor: '#3b82f6',
  viewport: {
    width: 'device-width',
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
    viewportFit: 'cover',
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: '柒零连锁',
  },
  formatDetection: {
    telephone: false,
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': '柒零连锁',
    'application-name': '柒零连锁',
    'msapplication-TileColor': '#3b82f6',
    'msapplication-config': '/browserconfig.xml',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className={`${inter.variable} font-sans antialiased`}>
        <ErrorBoundary>
          <AuthProvider>
            <Toaster />
            {children}
          </AuthProvider>
        </ErrorBoundary>

        {/* PWA Service Worker 注册脚本 */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }

              // 移动端视口调整
              if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)) {
                document.body.classList.add('mobile-device');
              }

              // 检测PWA模式
              if (window.matchMedia('(display-mode: standalone)').matches) {
                document.body.classList.add('pwa-mode');
              }

              // 初始化性能监控
              if (typeof window !== 'undefined') {
                import('/lib/performance/monitoring').then(({ performanceMonitor }) => {
                  // 性能监控已自动初始化
                  console.log('Performance monitoring initialized');
                });
              }

              // 全局错误处理
              window.addEventListener('error', function(event) {
                console.error('Global error:', event.error);
                // 发送错误到监控服务
                fetch('/api/errors', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    message: event.error?.message || 'Unknown error',
                    stack: event.error?.stack,
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno,
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                  }),
                }).catch(console.error);
              });

              // 未处理的Promise拒绝
              window.addEventListener('unhandledrejection', function(event) {
                console.error('Unhandled promise rejection:', event.reason);
                fetch('/api/errors', {
                  method: 'POST',
                  headers: { 'Content-Type': 'application/json' },
                  body: JSON.stringify({
                    message: 'Unhandled Promise Rejection',
                    reason: event.reason?.toString(),
                    timestamp: new Date().toISOString(),
                    url: window.location.href,
                    userAgent: navigator.userAgent,
                  }),
                }).catch(console.error);
              });
            `,
          }}
        />
      </body>
    </html>
  );
}
