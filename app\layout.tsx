import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { AuthProvider } from "@/lib/auth/context";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "柒零支付连锁 - 商家服务平台",
  description: "基于Next.js 15 + Supabase的现代全栈商家管理平台，支持酒店、餐饮、商品管理和多支付方式集成",
  keywords: ["商家管理", "酒店管理", "餐饮管理", "支付系统", "柒零支付"],
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="zh-CN">
      <body className={`${inter.variable} font-sans antialiased`}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
