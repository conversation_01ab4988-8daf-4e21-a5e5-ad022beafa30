/**
 * 餐饮相关类型定义
 * 扩展数据库类型，添加餐饮业务逻辑类型
 */

import type { Database } from './database'

// 从数据库类型中提取餐饮相关类型
export type Restaurant = Database['public']['Tables']['restaurants']['Row']
export type RestaurantInsert = Database['public']['Tables']['restaurants']['Insert']
export type RestaurantUpdate = Database['public']['Tables']['restaurants']['Update']

export type MenuItem = Database['public']['Tables']['menu_items']['Row']
export type MenuItemInsert = Database['public']['Tables']['menu_items']['Insert']
export type MenuItemUpdate = Database['public']['Tables']['menu_items']['Update']

export type MenuCategory = Database['public']['Tables']['menu_categories']['Row']
export type MenuCategoryInsert = Database['public']['Tables']['menu_categories']['Insert']
export type MenuCategoryUpdate = Database['public']['Tables']['menu_categories']['Update']

// 餐厅状态枚举
export type RestaurantStatus = 'active' | 'inactive' | 'maintenance'

// 菜品状态枚举
export type MenuItemStatus = 'available' | 'unavailable' | 'sold_out'

// 餐厅类型枚举
export const RESTAURANT_TYPES = [
  'chinese', 'western', 'japanese', 'korean', 'thai', 'indian',
  'italian', 'french', 'mexican', 'fast_food', 'cafe', 'bar',
  'buffet', 'hotpot', 'bbq', 'seafood', 'vegetarian', 'dessert'
] as const

export type RestaurantType = typeof RESTAURANT_TYPES[number]

// 菜品分类枚举
export const MENU_CATEGORIES = [
  'appetizer', 'soup', 'salad', 'main_course', 'side_dish',
  'dessert', 'beverage', 'alcohol', 'hot_drink', 'cold_drink',
  'breakfast', 'lunch', 'dinner', 'snack', 'special'
] as const

export type MenuCategoryType = typeof MENU_CATEGORIES[number]

// 餐厅设施类型
export const RESTAURANT_AMENITIES = [
  'wifi', 'parking', 'air_conditioning', 'outdoor_seating', 'private_room',
  'live_music', 'tv', 'wheelchair_accessible', 'pet_friendly', 'smoking_area',
  'non_smoking', 'takeaway', 'delivery', 'reservation', 'group_dining'
] as const

export type RestaurantAmenity = typeof RESTAURANT_AMENITIES[number]

// 营业时间类型
export interface BusinessHours {
  [key: string]: {
    isOpen: boolean
    openTime?: string
    closeTime?: string
    breakStart?: string
    breakEnd?: string
  }
}

// 餐厅详细信息类型（包含关联数据）
export interface RestaurantWithDetails extends Restaurant {
  menu_categories?: MenuCategoryWithItems[]
  menu_items?: MenuItemWithDetails[]
  total_orders?: number
  revenue?: number
  rating?: number
  reviews_count?: number
}

// 菜单分类详细信息类型
export interface MenuCategoryWithItems extends MenuCategory {
  menu_items?: MenuItemWithDetails[]
  items_count?: number
}

// 菜品详细信息类型
export interface MenuItemWithDetails extends MenuItem {
  category?: MenuCategory
  restaurant?: Restaurant
  orders_count?: number
  revenue?: number
}

// 餐厅表单数据类型
export interface RestaurantFormData {
  name: string
  description?: string
  restaurant_type?: RestaurantType[]
  address?: string
  phone?: string
  email?: string
  website?: string
  images?: string[]
  amenities?: RestaurantAmenity[]
  business_hours?: BusinessHours
  capacity?: number
  average_price?: number
  is_active?: boolean
}

// 菜品表单数据类型
export interface MenuItemFormData {
  restaurant_id: string
  category_id?: string
  name: string
  description?: string
  price: number
  original_price?: number
  images?: string[]
  ingredients?: string[]
  allergens?: string[]
  nutrition_info?: Record<string, any>
  preparation_time?: number
  is_spicy?: boolean
  is_vegetarian?: boolean
  is_vegan?: boolean
  is_gluten_free?: boolean
  is_available?: boolean
  sort_order?: number
}

// 菜单分类表单数据类型
export interface MenuCategoryFormData {
  restaurant_id: string
  name: string
  description?: string
  image?: string
  sort_order?: number
  is_active?: boolean
}

// 餐厅搜索参数类型
export interface RestaurantSearchParams {
  query?: string
  location?: string
  restaurant_type?: RestaurantType[]
  amenities?: RestaurantAmenity[]
  min_price?: number
  max_price?: number
  rating?: number
  is_open?: boolean
  sort_by?: 'name' | 'rating' | 'price' | 'distance' | 'created_at'
  sort_order?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// 菜品搜索参数类型
export interface MenuItemSearchParams {
  restaurant_id?: string
  category_id?: string
  query?: string
  min_price?: number
  max_price?: number
  is_vegetarian?: boolean
  is_vegan?: boolean
  is_spicy?: boolean
  is_available?: boolean
  sort_by?: 'name' | 'price' | 'popularity' | 'created_at'
  sort_order?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// 餐厅统计信息类型
export interface RestaurantStats {
  total_restaurants: number
  active_restaurants: number
  total_menu_items: number
  available_menu_items: number
  total_orders: number
  total_revenue: number
  average_rating: number
  popular_items: MenuItemWithDetails[]
}

// 餐厅操作结果类型
export interface RestaurantActionResult {
  success: boolean
  data?: Restaurant | MenuItem | MenuCategory
  error?: string
  message?: string
}

// 餐厅类型配置
export const RESTAURANT_TYPE_CONFIG = {
  chinese: { label: '中餐', icon: '🥢', color: 'red' },
  western: { label: '西餐', icon: '🍽️', color: 'blue' },
  japanese: { label: '日料', icon: '🍣', color: 'pink' },
  korean: { label: '韩料', icon: '🍲', color: 'orange' },
  thai: { label: '泰餐', icon: '🌶️', color: 'green' },
  indian: { label: '印度菜', icon: '🍛', color: 'yellow' },
  italian: { label: '意大利菜', icon: '🍝', color: 'green' },
  french: { label: '法餐', icon: '🥐', color: 'purple' },
  mexican: { label: '墨西哥菜', icon: '🌮', color: 'orange' },
  fast_food: { label: '快餐', icon: '🍔', color: 'red' },
  cafe: { label: '咖啡厅', icon: '☕', color: 'brown' },
  bar: { label: '酒吧', icon: '🍺', color: 'amber' },
  buffet: { label: '自助餐', icon: '🍽️', color: 'blue' },
  hotpot: { label: '火锅', icon: '🍲', color: 'red' },
  bbq: { label: '烧烤', icon: '🍖', color: 'orange' },
  seafood: { label: '海鲜', icon: '🦐', color: 'blue' },
  vegetarian: { label: '素食', icon: '🥗', color: 'green' },
  dessert: { label: '甜品', icon: '🍰', color: 'pink' },
} as const

// 设施配置
export const AMENITY_CONFIG = {
  wifi: { label: '免费WiFi', icon: '📶', category: 'connectivity' },
  parking: { label: '停车场', icon: '🅿️', category: 'convenience' },
  air_conditioning: { label: '空调', icon: '❄️', category: 'comfort' },
  outdoor_seating: { label: '户外座位', icon: '🌳', category: 'seating' },
  private_room: { label: '包间', icon: '🏠', category: 'seating' },
  live_music: { label: '现场音乐', icon: '🎵', category: 'entertainment' },
  tv: { label: '电视', icon: '📺', category: 'entertainment' },
  wheelchair_accessible: { label: '无障碍通道', icon: '♿', category: 'accessibility' },
  pet_friendly: { label: '宠物友好', icon: '🐕', category: 'policy' },
  smoking_area: { label: '吸烟区', icon: '🚬', category: 'policy' },
  non_smoking: { label: '禁烟', icon: '🚭', category: 'policy' },
  takeaway: { label: '外带', icon: '🥡', category: 'service' },
  delivery: { label: '外送', icon: '🚚', category: 'service' },
  reservation: { label: '预订', icon: '📞', category: 'service' },
  group_dining: { label: '团体用餐', icon: '👥', category: 'service' },
} as const

// 菜单分类配置
export const MENU_CATEGORY_CONFIG = {
  appetizer: { label: '开胃菜', icon: '🥗', order: 1 },
  soup: { label: '汤品', icon: '🍲', order: 2 },
  salad: { label: '沙拉', icon: '🥙', order: 3 },
  main_course: { label: '主菜', icon: '🍖', order: 4 },
  side_dish: { label: '配菜', icon: '🥔', order: 5 },
  dessert: { label: '甜品', icon: '🍰', order: 6 },
  beverage: { label: '饮料', icon: '🥤', order: 7 },
  alcohol: { label: '酒类', icon: '🍷', order: 8 },
  hot_drink: { label: '热饮', icon: '☕', order: 9 },
  cold_drink: { label: '冷饮', icon: '🧊', order: 10 },
  breakfast: { label: '早餐', icon: '🍳', order: 11 },
  lunch: { label: '午餐', icon: '🍱', order: 12 },
  dinner: { label: '晚餐', icon: '🍽️', order: 13 },
  snack: { label: '小食', icon: '🍿', order: 14 },
  special: { label: '特色菜', icon: '⭐', order: 15 },
} as const

// 默认营业时间
export const DEFAULT_BUSINESS_HOURS: BusinessHours = {
  monday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
  tuesday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
  wednesday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
  thursday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
  friday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
  saturday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
  sunday: { isOpen: true, openTime: '09:00', closeTime: '22:00' },
}

// 星期配置
export const WEEKDAYS = {
  monday: '周一',
  tuesday: '周二',
  wednesday: '周三',
  thursday: '周四',
  friday: '周五',
  saturday: '周六',
  sunday: '周日',
} as const
