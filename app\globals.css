@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #2563eb;
  --primary-foreground: #ffffff;
  --secondary: #f1f5f9;
  --secondary-foreground: #0f172a;
  --muted: #f8fafc;
  --muted-foreground: #64748b;
  --accent: #f1f5f9;
  --accent-foreground: #0f172a;
  --destructive: #ef4444;
  --destructive-foreground: #ffffff;
  --border: #e2e8f0;
  --input: #e2e8f0;
  --ring: #2563eb;
  --radius: 0.5rem;
  --card: #ffffff;
  --card-foreground: #171717;
  --popover: #ffffff;
  --popover-foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-card: var(--background);
  --color-card-foreground: var(--foreground);
  --color-popover: var(--background);
  --color-popover-foreground: var(--foreground);
  --font-sans: var(--font-inter);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #3b82f6;
    --primary-foreground: #ffffff;
    --secondary: #1e293b;
    --secondary-foreground: #f8fafc;
    --muted: #1e293b;
    --muted-foreground: #94a3b8;
    --accent: #1e293b;
    --accent-foreground: #f8fafc;
    --destructive: #f87171;
    --destructive-foreground: #ffffff;
    --border: #334155;
    --input: #334155;
    --ring: #3b82f6;
    --card: #0a0a0a;
    --card-foreground: #ededed;
    --popover: #0a0a0a;
    --popover-foreground: #ededed;
  }
}

/* 移动端优化样式 */
@media (max-width: 768px) {
  /* 触摸友好的按钮尺寸 */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* 移动端表格优化 */
  .mobile-table {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .mobile-table table {
    width: 100%;
    min-width: 600px;
  }

  /* 移动端表单优化 */
  .mobile-form input,
  .mobile-form select,
  .mobile-form textarea {
    font-size: 16px; /* 防止iOS缩放 */
    padding: 12px;
  }

  /* 移动端卡片间距 */
  .mobile-cards {
    gap: 12px;
  }

  /* 移动端导航优化 */
  .mobile-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
    background: hsl(var(--background));
    border-top: 1px solid hsl(var(--border));
  }

  /* 移动端页面内边距 */
  .mobile-page {
    padding: 16px;
    padding-bottom: 80px; /* 为底部导航留空间 */
    padding-top: 64px; /* 为顶部导航留空间 */
  }

  /* 移动端文字大小调整 */
  .mobile-text-sm {
    font-size: 14px;
  }

  .mobile-text-xs {
    font-size: 12px;
  }

  /* 移动端滚动优化 */
  .mobile-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 移动端模态框优化 */
  .mobile-modal {
    margin: 0;
    max-height: 90vh;
    border-radius: 12px 12px 0 0;
  }

  /* 移动端下拉菜单优化 */
  .mobile-dropdown {
    max-height: 60vh;
    overflow-y: auto;
  }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1024px) {
  .tablet-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .tablet-sidebar {
    width: 280px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  /* 移除hover效果，使用active状态 */
  .hover\:bg-muted:hover {
    background-color: transparent;
  }

  .hover\:bg-muted:active {
    background-color: hsl(var(--muted));
  }

  /* 增大可点击区域 */
  button,
  [role="button"],
  .clickable {
    min-height: 44px;
    min-width: 44px;
  }

  /* 优化选择文本 */
  .selectable {
    -webkit-user-select: text;
    user-select: text;
  }

  .non-selectable {
    -webkit-user-select: none;
    user-select: none;
  }
}

/* PWA 样式 */
@media (display-mode: standalone) {
  /* PWA 模式下的样式调整 */
  .pwa-app {
    padding-top: env(safe-area-inset-top);
    padding-bottom: env(safe-area-inset-bottom);
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* 隐藏浏览器相关的UI元素 */
  .browser-only {
    display: none;
  }
}

/* 深色模式移动端优化 */
@media (prefers-color-scheme: dark) {
  .dark-mobile {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  .dark-mobile .mobile-nav {
    background-color: hsl(var(--card));
    border-top-color: hsl(var(--border));
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .high-contrast {
    border-width: 2px;
  }

  .high-contrast button {
    border: 2px solid currentColor;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .reduce-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 横屏模式优化 */
@media (orientation: landscape) and (max-height: 500px) {
  .landscape-mobile {
    padding-top: 8px;
    padding-bottom: 8px;
  }

  .landscape-mobile .mobile-nav {
    height: 60px;
  }

  .landscape-mobile .mobile-page {
    padding-top: 60px;
    padding-bottom: 60px;
  }
}

/* 可访问性优化 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.focus-visible:focus-visible {
  outline: 2px solid hsl(var(--ring));
  outline-offset: 2px;
}

/* 移动端特定的工具类 */
.mobile-only {
  display: none;
}

.desktop-only {
  display: block;
}

@media (max-width: 768px) {
  .mobile-only {
    display: block;
  }

  .desktop-only {
    display: none;
  }

  .mobile-flex {
    display: flex;
  }

  .mobile-grid {
    display: grid;
  }

  .mobile-hidden {
    display: none;
  }

  .mobile-full-width {
    width: 100%;
  }

  .mobile-text-center {
    text-align: center;
  }

  .mobile-p-4 {
    padding: 1rem;
  }

  .mobile-m-2 {
    margin: 0.5rem;
  }

  .mobile-rounded-lg {
    border-radius: 0.5rem;
  }
}

/* 安全区域适配 */
.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}

.safe-area-left {
  padding-left: env(safe-area-inset-left);
}

.safe-area-right {
  padding-right: env(safe-area-inset-right);
}

/* 移动端性能优化 */
.gpu-accelerated {
  transform: translateZ(0);
  will-change: transform;
}

.smooth-scroll {
  scroll-behavior: smooth;
  -webkit-overflow-scrolling: touch;
}

/* 移动端手势支持 */
.swipeable {
  touch-action: pan-x;
}

.pinch-zoomable {
  touch-action: pinch-zoom;
}

.no-touch-action {
  touch-action: none;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), system-ui, sans-serif;
}

* {
  border-color: var(--border);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--muted-foreground);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--foreground);
}
