/**
 * 错误边界组件
 * 捕获和处理React组件错误
 */

'use client'

import React, { Component, ErrorInfo, ReactNode } from 'react'
import { AlertTriangle, RefreshCw, Home, Bug } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
  errorId: string
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    }
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    // 更新state以显示错误UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // 记录错误信息
    this.setState({
      error,
      errorInfo,
    })

    // 调用外部错误处理函数
    this.props.onError?.(error, errorInfo)

    // 发送错误到监控服务
    this.logErrorToService(error, errorInfo)
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    try {
      // 这里可以集成错误监控服务，如Sentry、LogRocket等
      const errorData = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: localStorage.getItem('userId') || 'anonymous',
        errorId: this.state.errorId,
      }

      // 发送到后端API
      fetch('/api/errors', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData),
      }).catch(err => {
        console.error('Failed to log error:', err)
      })

      // 本地存储错误信息
      const errors = JSON.parse(localStorage.getItem('app_errors') || '[]')
      errors.push(errorData)
      // 只保留最近10个错误
      if (errors.length > 10) {
        errors.splice(0, errors.length - 10)
      }
      localStorage.setItem('app_errors', JSON.stringify(errors))
    } catch (err) {
      console.error('Error logging failed:', err)
    }
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: '',
    })
  }

  private handleReload = () => {
    window.location.reload()
  }

  private handleGoHome = () => {
    window.location.href = '/'
  }

  private copyErrorInfo = () => {
    const errorText = `
错误ID: ${this.state.errorId}
错误信息: ${this.state.error?.message}
错误堆栈: ${this.state.error?.stack}
组件堆栈: ${this.state.errorInfo?.componentStack}
时间: ${new Date().toISOString()}
URL: ${window.location.href}
    `.trim()

    navigator.clipboard.writeText(errorText).then(() => {
      alert('错误信息已复制到剪贴板')
    }).catch(() => {
      // 降级方案
      const textArea = document.createElement('textarea')
      textArea.value = errorText
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)
      alert('错误信息已复制到剪贴板')
    })
  }

  render() {
    if (this.state.hasError) {
      // 如果提供了自定义fallback，使用它
      if (this.props.fallback) {
        return this.props.fallback
      }

      // 默认错误UI
      return (
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <Card className="w-full max-w-2xl">
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 bg-red-100 rounded-full">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <CardTitle className="text-xl text-red-600">应用程序错误</CardTitle>
                  <CardDescription>
                    很抱歉，应用程序遇到了一个意外错误
                  </CardDescription>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-6">
              {/* 错误信息 */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-red-900">错误详情</h4>
                  <Badge variant="outline" className="text-xs">
                    ID: {this.state.errorId}
                  </Badge>
                </div>
                <p className="text-sm text-red-800 mb-2">
                  {this.state.error?.message || '未知错误'}
                </p>
                {process.env.NODE_ENV === 'development' && (
                  <details className="text-xs text-red-700">
                    <summary className="cursor-pointer font-medium mb-2">
                      技术详情 (开发模式)
                    </summary>
                    <pre className="whitespace-pre-wrap bg-red-100 p-2 rounded text-xs overflow-auto max-h-40">
                      {this.state.error?.stack}
                    </pre>
                    {this.state.errorInfo?.componentStack && (
                      <pre className="whitespace-pre-wrap bg-red-100 p-2 rounded text-xs overflow-auto max-h-40 mt-2">
                        组件堆栈:{this.state.errorInfo.componentStack}
                      </pre>
                    )}
                  </details>
                )}
              </div>

              {/* 解决建议 */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">解决建议</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• 尝试刷新页面</li>
                  <li>• 检查网络连接</li>
                  <li>• 清除浏览器缓存</li>
                  <li>• 如果问题持续，请联系技术支持</li>
                </ul>
              </div>

              {/* 操作按钮 */}
              <div className="flex flex-wrap gap-3">
                <Button onClick={this.handleRetry} className="flex-1">
                  <RefreshCw className="h-4 w-4 mr-2" />
                  重试
                </Button>
                
                <Button variant="outline" onClick={this.handleReload} className="flex-1">
                  刷新页面
                </Button>
                
                <Button variant="outline" onClick={this.handleGoHome}>
                  <Home className="h-4 w-4 mr-2" />
                  返回首页
                </Button>
                
                <Button variant="outline" onClick={this.copyErrorInfo}>
                  <Bug className="h-4 w-4 mr-2" />
                  复制错误信息
                </Button>
              </div>

              {/* 联系支持 */}
              <div className="text-center pt-4 border-t">
                <p className="text-sm text-muted-foreground mb-2">
                  如果问题持续存在，请联系我们的技术支持团队
                </p>
                <div className="flex justify-center space-x-4 text-sm">
                  <a 
                    href="mailto:<EMAIL>" 
                    className="text-blue-600 hover:underline"
                  >
                    📧 <EMAIL>
                  </a>
                  <a 
                    href="tel:************" 
                    className="text-blue-600 hover:underline"
                  >
                    📞 ************
                  </a>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// 高阶组件包装器
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// 简化的错误边界Hook（用于函数组件）
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    // 手动触发错误边界
    throw error
  }
}
