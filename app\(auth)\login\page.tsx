/**
 * 登录页面
 * 用户登录界面
 */

'use client'

import { useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { AuthForm } from '@/components/forms/auth-form'
import { login } from '@/lib/auth/actions'
import type { LoginFormData } from '@/lib/validations/auth'

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const router = useRouter()
  const searchParams = useSearchParams()
  const redirectTo = searchParams.get('redirectTo') || '/dashboard'

  const handleLogin = async (data: LoginFormData) => {
    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      const result = await login(data)

      if (result.success) {
        setSuccess(result.message || '登录成功')
        // 延迟跳转，让用户看到成功消息
        setTimeout(() => {
          router.push(redirectTo)
        }, 1000)
      } else {
        setError(result.error || '登录失败')
      }
    } catch (err) {
      setError('登录失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="w-full max-w-md mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-8">
        {/* 头部 */}
        <div className="text-center mb-8">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            登录账户
          </h1>
          <p className="text-gray-600">
            欢迎回到柒零支付连锁管理平台
          </p>
        </div>

        {/* 错误提示 */}
        {error && (
          <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-600">{error}</p>
          </div>
        )}

        {/* 成功提示 */}
        {success && (
          <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-600">{success}</p>
          </div>
        )}

        {/* 登录表单 */}
        <AuthForm
          type="login"
          onSubmit={handleLogin}
          isLoading={isLoading}
        />

        {/* 底部链接 */}
        <div className="mt-6 space-y-4">
          <div className="text-center">
            <Link
              href="/forgot-password"
              className="text-sm text-blue-600 hover:text-blue-500"
            >
              忘记密码？
            </Link>
          </div>
          
          <div className="text-center">
            <span className="text-sm text-gray-600">
              还没有账户？{' '}
              <Link
                href="/register"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                立即注册
              </Link>
            </span>
          </div>
        </div>
      </div>

      {/* 返回首页 */}
      <div className="mt-6 text-center">
        <Link
          href="/"
          className="text-sm text-gray-500 hover:text-gray-700"
        >
          ← 返回首页
        </Link>
      </div>
    </div>
  )
}
