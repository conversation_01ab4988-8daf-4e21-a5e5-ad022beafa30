/**
 * 顶部导航栏组件
 * 包含用户菜单、通知、搜索等功能
 */

'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { 
  Bell, 
  Search, 
  User, 
  Settings, 
  LogOut,
  ChevronDown
} from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/lib/auth/context'
import { logout } from '@/lib/auth/actions'

interface NavbarProps {
  className?: string
}

export function Navbar({ className }: NavbarProps) {
  const [showUserMenu, setShowUserMenu] = useState(false)
  const [showNotifications, setShowNotifications] = useState(false)
  const { user } = useAuth()
  const router = useRouter()

  const handleLogout = async () => {
    try {
      await logout()
      router.push('/login')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  // 模拟通知数据
  const notifications = [
    { id: 1, title: '新订单提醒', message: '您有一个新的酒店预订订单', time: '5分钟前', unread: true },
    { id: 2, title: '库存预警', message: '商品"柒零毛巾套装"库存不足', time: '1小时前', unread: true },
    { id: 3, title: '支付成功', message: '订单 #20250701001 支付成功', time: '2小时前', unread: false },
  ]

  const unreadCount = notifications.filter(n => n.unread).length

  return (
    <header className={`sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6 ${className}`}>
      {/* 搜索框 */}
      <div className="flex-1 max-w-md">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="搜索订单、商品、客户..."
            className="pl-8"
          />
        </div>
      </div>

      {/* 右侧操作区 */}
      <div className="flex items-center gap-2">
        {/* 通知 */}
        <div className="relative">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setShowNotifications(!showNotifications)}
            className="relative"
          >
            <Bell className="h-4 w-4" />
            {unreadCount > 0 && (
              <Badge 
                variant="destructive" 
                className="absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs"
              >
                {unreadCount}
              </Badge>
            )}
          </Button>

          {/* 通知下拉菜单 */}
          {showNotifications && (
            <div className="absolute right-0 top-full mt-2 w-80 rounded-md border bg-popover p-0 shadow-md">
              <div className="flex items-center justify-between border-b p-4">
                <h3 className="font-semibold">通知</h3>
                <Button variant="ghost" size="sm">
                  全部已读
                </Button>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {notifications.map((notification) => (
                  <div
                    key={notification.id}
                    className={`border-b p-4 hover:bg-accent ${
                      notification.unread ? 'bg-muted/50' : ''
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className={`mt-1 h-2 w-2 rounded-full ${
                        notification.unread ? 'bg-primary' : 'bg-transparent'
                      }`} />
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium">{notification.title}</p>
                        <p className="text-sm text-muted-foreground">{notification.message}</p>
                        <p className="text-xs text-muted-foreground">{notification.time}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              <div className="border-t p-2">
                <Button variant="ghost" className="w-full" size="sm">
                  查看全部通知
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* 用户菜单 */}
        <div className="relative">
          <Button
            variant="ghost"
            className="flex items-center gap-2 px-2"
            onClick={() => setShowUserMenu(!showUserMenu)}
          >
            <Avatar className="h-8 w-8">
              <AvatarImage src={user?.avatar} alt={user?.name || user?.email} />
              <AvatarFallback>
                {user?.name ? user.name[0].toUpperCase() : user?.email?.[0].toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="hidden md:block text-left">
              <p className="text-sm font-medium">
                {user?.name || user?.email}
              </p>
              <p className="text-xs text-muted-foreground">
                {user?.merchant?.name || '商家用户'}
              </p>
            </div>
            <ChevronDown className="h-4 w-4" />
          </Button>

          {/* 用户下拉菜单 */}
          {showUserMenu && (
            <div className="absolute right-0 top-full mt-2 w-56 rounded-md border bg-popover p-1 shadow-md">
              <div className="px-2 py-1.5 text-sm font-medium">
                {user?.name || user?.email}
              </div>
              <div className="px-2 py-1.5 text-xs text-muted-foreground">
                {user?.email}
              </div>
              <div className="my-1 h-px bg-border" />
              
              <Link href="/profile">
                <Button variant="ghost" className="w-full justify-start" size="sm">
                  <User className="mr-2 h-4 w-4" />
                  商家资料
                </Button>
              </Link>
              
              <Link href="/settings">
                <Button variant="ghost" className="w-full justify-start" size="sm">
                  <Settings className="mr-2 h-4 w-4" />
                  系统设置
                </Button>
              </Link>
              
              <div className="my-1 h-px bg-border" />
              
              <Button
                variant="ghost"
                className="w-full justify-start text-destructive hover:text-destructive"
                size="sm"
                onClick={handleLogout}
              >
                <LogOut className="mr-2 h-4 w-4" />
                退出登录
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* 点击外部关闭菜单 */}
      {(showUserMenu || showNotifications) && (
        <div
          className="fixed inset-0 z-20"
          onClick={() => {
            setShowUserMenu(false)
            setShowNotifications(false)
          }}
        />
      )}
    </header>
  )
}
