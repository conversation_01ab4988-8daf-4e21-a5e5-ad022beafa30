/**
 * 仪表板页面
 * 用户登录后的主页面
 */

'use client'

import { useAuth } from '@/lib/auth/context'
import { logout } from '@/lib/auth/actions'

export default function DashboardPage() {
  const { user, loading } = useAuth()

  const handleLogout = async () => {
    try {
      await logout()
      window.location.href = '/login'
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            请先登录
          </h1>
          <a
            href="/login"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            前往登录
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 欢迎区域 */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              欢迎回来，{user.name || user.email}！
            </h1>
            <p className="text-gray-600 mt-1">
              {user.merchant?.name || '您的商家管理平台'}
            </p>
          </div>
          <button
            onClick={handleLogout}
            className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
          >
            登出
          </button>
        </div>
      </div>

      {/* 用户信息卡片 */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          账户信息
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              邮箱地址
            </label>
            <p className="text-sm text-gray-900">{user.email}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              姓名
            </label>
            <p className="text-sm text-gray-900">{user.name || '未设置'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              手机号码
            </label>
            <p className="text-sm text-gray-900">{user.phone || '未设置'}</p>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              邮箱验证状态
            </label>
            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
              user.emailConfirmed 
                ? 'bg-green-100 text-green-800'
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {user.emailConfirmed ? '已验证' : '未验证'}
            </span>
          </div>
        </div>
      </div>

      {/* 商家信息卡片 */}
      {user.merchant && (
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            商家信息
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                商家名称
              </label>
              <p className="text-sm text-gray-900">{user.merchant.name}</p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                业务类型
              </label>
              <div className="flex flex-wrap gap-1">
                {user.merchant.business_types?.map((type: string) => (
                  <span
                    key={type}
                    className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800"
                  >
                    {type === 'hotel' && '酒店管理'}
                    {type === 'restaurant' && '餐饮管理'}
                    {type === 'product' && '商品管理'}
                  </span>
                ))}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                商家状态
              </label>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                user.merchant.status === 'active' 
                  ? 'bg-green-100 text-green-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {user.merchant.status === 'active' ? '正常营业' : '暂停营业'}
              </span>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                注册时间
              </label>
              <p className="text-sm text-gray-900">
                {new Date(user.createdAt).toLocaleDateString('zh-CN')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* 快速操作 */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">
          快速操作
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {user.merchant?.business_types?.includes('hotel') && (
            <a
              href="/hotels"
              className="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-colors"
            >
              <div className="text-2xl mb-2">🏨</div>
              <h3 className="font-medium text-gray-900">酒店管理</h3>
              <p className="text-sm text-gray-600">管理酒店信息和房间</p>
            </a>
          )}
          
          {user.merchant?.business_types?.includes('restaurant') && (
            <a
              href="/restaurants"
              className="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-colors"
            >
              <div className="text-2xl mb-2">🍽️</div>
              <h3 className="font-medium text-gray-900">餐饮管理</h3>
              <p className="text-sm text-gray-600">管理餐厅和菜单</p>
            </a>
          )}
          
          {user.merchant?.business_types?.includes('product') && (
            <a
              href="/products"
              className="block p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-sm transition-colors"
            >
              <div className="text-2xl mb-2">🛍️</div>
              <h3 className="font-medium text-gray-900">商品管理</h3>
              <p className="text-sm text-gray-600">管理商品和库存</p>
            </a>
          )}
        </div>
      </div>
    </div>
  )
}
