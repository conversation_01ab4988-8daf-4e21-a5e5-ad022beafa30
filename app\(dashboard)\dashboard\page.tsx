/**
 * 仪表板页面
 * 用户登录后的主页面
 */

'use client'

import { useAuth } from '@/lib/auth/context'
import { PageContainer } from '@/components/layouts/page-container'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'

export default function DashboardPage() {
  const { user, loading } = useAuth()

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            请先登录
          </h1>
          <a
            href="/login"
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            前往登录
          </a>
        </div>
      </div>
    )
  }

  return (
    <PageContainer
      title={`欢迎回来，${user.name || user.email}！`}
      description={user.merchant?.name || '您的商家管理平台'}
      breadcrumbs={[
        { title: '仪表板' }
      ]}
    >
      <div className="space-y-6">

        {/* 用户信息卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>账户信息</CardTitle>
            <CardDescription>您的个人账户详细信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  邮箱地址
                </label>
                <p className="text-sm">{user.email}</p>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  姓名
                </label>
                <p className="text-sm">{user.name || '未设置'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  手机号码
                </label>
                <p className="text-sm">{user.phone || '未设置'}</p>
              </div>
              <div>
                <label className="block text-sm font-medium mb-1">
                  邮箱验证状态
                </label>
                <Badge variant={user.emailConfirmed ? 'success' : 'warning'}>
                  {user.emailConfirmed ? '已验证' : '未验证'}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 商家信息卡片 */}
        {user.merchant && (
          <Card>
            <CardHeader>
              <CardTitle>商家信息</CardTitle>
              <CardDescription>您的商家详细信息和业务配置</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    商家名称
                  </label>
                  <p className="text-sm">{user.merchant.name}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    业务类型
                  </label>
                  <div className="flex flex-wrap gap-1">
                    {user.merchant.business_types?.map((type: string) => (
                      <Badge key={type} variant="secondary">
                        {type === 'hotel' && '酒店管理'}
                        {type === 'restaurant' && '餐饮管理'}
                        {type === 'product' && '商品管理'}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    商家状态
                  </label>
                  <Badge variant={user.merchant.status === 'active' ? 'success' : 'secondary'}>
                    {user.merchant.status === 'active' ? '正常营业' : '暂停营业'}
                  </Badge>
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    注册时间
                  </label>
                  <p className="text-sm">
                    {new Date(user.createdAt).toLocaleDateString('zh-CN')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 快速操作 */}
        <Card>
          <CardHeader>
            <CardTitle>快速操作</CardTitle>
            <CardDescription>快速访问您的主要业务功能</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {user.merchant?.business_types?.includes('hotel') && (
                <Card className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-4">
                    <div className="text-2xl mb-2">🏨</div>
                    <h3 className="font-medium mb-1">酒店管理</h3>
                    <p className="text-sm text-muted-foreground">管理酒店信息和房间</p>
                  </CardContent>
                </Card>
              )}

              {user.merchant?.business_types?.includes('restaurant') && (
                <Card className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-4">
                    <div className="text-2xl mb-2">🍽️</div>
                    <h3 className="font-medium mb-1">餐饮管理</h3>
                    <p className="text-sm text-muted-foreground">管理餐厅和菜单</p>
                  </CardContent>
                </Card>
              )}

              {user.merchant?.business_types?.includes('product') && (
                <Card className="hover:shadow-md transition-shadow cursor-pointer">
                  <CardContent className="p-4">
                    <div className="text-2xl mb-2">🛍️</div>
                    <h3 className="font-medium mb-1">商品管理</h3>
                    <p className="text-sm text-muted-foreground">管理商品和库存</p>
                  </CardContent>
                </Card>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </PageContainer>
  )
}
