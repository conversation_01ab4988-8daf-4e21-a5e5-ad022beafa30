/**
 * 菜单管理组件
 * 管理餐厅的菜单分类和菜品
 */

'use client'

import { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, ChefHat, Tag, DollarSign } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { getMenuCategories, createMenuCategory, createMenuItem } from '@/lib/actions/restaurants'
import { 
  MENU_CATEGORY_CONFIG,
  type RestaurantWithDetails,
  type MenuCategoryWithItems,
  type MenuItemWithDetails
} from '@/types/restaurant'

interface MenuManagementProps {
  restaurant: RestaurantWithDetails
  onMenuUpdated?: () => void
}

export function MenuManagement({ restaurant, onMenuUpdated }: MenuManagementProps) {
  const [categories, setCategories] = useState<MenuCategoryWithItems[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null)
  const [showCategoryDialog, setShowCategoryDialog] = useState(false)
  const [showItemDialog, setShowItemDialog] = useState(false)

  useEffect(() => {
    loadMenuCategories()
  }, [restaurant.id])

  const loadMenuCategories = async () => {
    setIsLoading(true)

    try {
      const result = await getMenuCategories(restaurant.id)

      if (result.success && Array.isArray(result.data)) {
        setCategories(result.data)
      }
    } catch (error) {
      console.error('Load menu categories error:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleCategoryCreated = () => {
    setShowCategoryDialog(false)
    loadMenuCategories()
    onMenuUpdated?.()
  }

  const handleItemCreated = () => {
    setShowItemDialog(false)
    loadMenuCategories()
    onMenuUpdated?.()
  }

  // 过滤菜品
  const filteredCategories = categories.map(category => ({
    ...category,
    menu_items: category.menu_items?.filter(item =>
      item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchQuery.toLowerCase())
    ) || []
  })).filter(category => 
    category.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (category.menu_items && category.menu_items.length > 0)
  )

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 头部操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex-1 max-w-md">
          <Input
            placeholder="搜索菜品或分类..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <div className="flex items-center space-x-2">
          <Dialog open={showCategoryDialog} onOpenChange={setShowCategoryDialog}>
            <DialogTrigger asChild>
              <Button variant="outline">
                <Tag className="h-4 w-4 mr-2" />
                添加分类
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>添加菜单分类</DialogTitle>
                <DialogDescription>
                  为餐厅添加新的菜单分类
                </DialogDescription>
              </DialogHeader>
              <CategoryForm 
                restaurantId={restaurant.id} 
                onSuccess={handleCategoryCreated}
                onCancel={() => setShowCategoryDialog(false)}
              />
            </DialogContent>
          </Dialog>

          <Dialog open={showItemDialog} onOpenChange={setShowItemDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                添加菜品
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>添加菜品</DialogTitle>
                <DialogDescription>
                  为餐厅添加新的菜品
                </DialogDescription>
              </DialogHeader>
              <MenuItemForm 
                restaurantId={restaurant.id}
                categories={categories}
                onSuccess={handleItemCreated}
                onCancel={() => setShowItemDialog(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* 菜单列表 */}
      {filteredCategories.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center h-64">
            <div className="text-center">
              <ChefHat className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground mb-4">
                {searchQuery ? '没有找到匹配的菜品' : '还没有添加任何菜单'}
              </p>
              {!searchQuery && (
                <div className="space-x-2">
                  <Button variant="outline" onClick={() => setShowCategoryDialog(true)}>
                    添加分类
                  </Button>
                  <Button onClick={() => setShowItemDialog(true)}>
                    添加菜品
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {filteredCategories.map((category) => (
            <Card key={category.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="flex items-center space-x-2">
                      <span>{MENU_CATEGORY_CONFIG[category.name as keyof typeof MENU_CATEGORY_CONFIG]?.icon || '📋'}</span>
                      <span>{category.name}</span>
                      <Badge variant="outline" className="text-xs">
                        {category.menu_items?.length || 0} 道菜
                      </Badge>
                    </CardTitle>
                    {category.description && (
                      <CardDescription className="mt-1">
                        {category.description}
                      </CardDescription>
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              
              {category.menu_items && category.menu_items.length > 0 && (
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {category.menu_items.map((item) => (
                      <div key={item.id} className="border rounded-lg p-4 space-y-3">
                        {/* 菜品图片 */}
                        {item.images && item.images.length > 0 ? (
                          <img
                            src={item.images[0]}
                            alt={item.name}
                            className="w-full h-32 object-cover rounded-md"
                          />
                        ) : (
                          <div className="w-full h-32 bg-muted rounded-md flex items-center justify-center">
                            <ChefHat className="h-8 w-8 text-muted-foreground" />
                          </div>
                        )}

                        {/* 菜品信息 */}
                        <div className="space-y-2">
                          <div className="flex items-start justify-between">
                            <h4 className="font-medium">{item.name}</h4>
                            <Badge variant={item.is_available ? 'success' : 'secondary'} className="text-xs">
                              {item.is_available ? '有售' : '售罄'}
                            </Badge>
                          </div>

                          {item.description && (
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {item.description}
                            </p>
                          )}

                          {/* 价格 */}
                          <div className="flex items-center space-x-2">
                            <span className="text-lg font-bold text-primary">
                              {formatCurrency(item.price)}
                            </span>
                            {item.original_price && item.original_price > item.price && (
                              <span className="text-sm text-muted-foreground line-through">
                                {formatCurrency(item.original_price)}
                              </span>
                            )}
                          </div>

                          {/* 标签 */}
                          <div className="flex flex-wrap gap-1">
                            {item.is_spicy && (
                              <Badge variant="destructive" className="text-xs">🌶️ 辣</Badge>
                            )}
                            {item.is_vegetarian && (
                              <Badge variant="success" className="text-xs">🥬 素食</Badge>
                            )}
                            {item.is_vegan && (
                              <Badge variant="success" className="text-xs">🌱 纯素</Badge>
                            )}
                            {item.is_gluten_free && (
                              <Badge variant="outline" className="text-xs">无麸质</Badge>
                            )}
                          </div>

                          {/* 操作按钮 */}
                          <div className="flex items-center justify-between pt-2">
                            <div className="flex items-center space-x-1">
                              <Button variant="outline" size="sm">
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                            {item.preparation_time && (
                              <span className="text-xs text-muted-foreground">
                                {item.preparation_time}分钟
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              )}
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}

// 简化的分类表单组件
function CategoryForm({ restaurantId, onSuccess, onCancel }: {
  restaurantId: string
  onSuccess: () => void
  onCancel: () => void
}) {
  const [name, setName] = useState('')
  const [description, setDescription] = useState('')
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!name.trim()) return

    setIsLoading(true)

    try {
      const result = await createMenuCategory({
        restaurant_id: restaurantId,
        name: name.trim(),
        description: description.trim() || undefined,
      })

      if (result.success) {
        onSuccess()
      } else {
        alert(result.error || '创建分类失败')
      }
    } catch (error) {
      alert('创建分类失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-2">分类名称</label>
        <Input
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="请输入分类名称"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium mb-2">分类描述</label>
        <Input
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="请输入分类描述（可选）"
        />
      </div>
      <div className="flex space-x-2">
        <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
          取消
        </Button>
        <Button type="submit" disabled={isLoading} className="flex-1">
          {isLoading ? '创建中...' : '创建分类'}
        </Button>
      </div>
    </form>
  )
}

// 简化的菜品表单组件
function MenuItemForm({ restaurantId, categories, onSuccess, onCancel }: {
  restaurantId: string
  categories: MenuCategoryWithItems[]
  onSuccess: () => void
  onCancel: () => void
}) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    price: '',
    category_id: '',
  })
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim() || !formData.price) return

    setIsLoading(true)

    try {
      const result = await createMenuItem({
        restaurant_id: restaurantId,
        category_id: formData.category_id || undefined,
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        price: parseFloat(formData.price),
      })

      if (result.success) {
        onSuccess()
      } else {
        alert(result.error || '创建菜品失败')
      }
    } catch (error) {
      alert('创建菜品失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-2">菜品名称</label>
        <Input
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
          placeholder="请输入菜品名称"
          required
        />
      </div>
      <div>
        <label className="block text-sm font-medium mb-2">菜品描述</label>
        <Input
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
          placeholder="请输入菜品描述（可选）"
        />
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium mb-2">价格</label>
          <Input
            type="number"
            step="0.01"
            min="0"
            value={formData.price}
            onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
            placeholder="0.00"
            required
          />
        </div>
        <div>
          <label className="block text-sm font-medium mb-2">分类</label>
          <select
            value={formData.category_id}
            onChange={(e) => setFormData(prev => ({ ...prev, category_id: e.target.value }))}
            className="w-full px-3 py-2 border border-input rounded-md"
          >
            <option value="">选择分类（可选）</option>
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
      </div>
      <div className="flex space-x-2">
        <Button type="button" variant="outline" onClick={onCancel} className="flex-1">
          取消
        </Button>
        <Button type="submit" disabled={isLoading} className="flex-1">
          {isLoading ? '创建中...' : '创建菜品'}
        </Button>
      </div>
    </form>
  )
}
