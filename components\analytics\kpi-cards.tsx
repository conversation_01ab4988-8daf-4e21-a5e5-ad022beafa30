/**
 * KPI指标卡片组件
 * 显示关键业务指标
 */

'use client'

import { TrendingUp, TrendingDown, Minus, DollarSign, ShoppingCart, Users, Package } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import type { KPIMetric } from '@/types/analytics'

interface KPICardProps {
  metric: KPIMetric
  className?: string
}

export function KPICard({ metric, className = '' }: KPICardProps) {
  const getTrendIcon = () => {
    switch (metric.trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-500" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-500" />
      default:
        return <Minus className="h-4 w-4 text-gray-500" />
    }
  }

  const getTrendColor = () => {
    switch (metric.trend) {
      case 'up':
        return 'text-green-600'
      case 'down':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  const formatValue = (value: number) => {
    switch (metric.format) {
      case 'currency':
        return `¥${value.toLocaleString()}`
      case 'percentage':
        return `${value.toFixed(1)}%`
      default:
        return value.toLocaleString()
    }
  }

  const getMetricIcon = () => {
    switch (metric.id) {
      case 'revenue':
        return <DollarSign className="h-5 w-5" />
      case 'orders':
        return <ShoppingCart className="h-5 w-5" />
      case 'customers':
        return <Users className="h-5 w-5" />
      case 'products':
        return <Package className="h-5 w-5" />
      default:
        return null
    }
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center space-x-2">
          {getMetricIcon()}
          <span>{metric.name}</span>
        </CardTitle>
        {metric.target && (
          <Badge variant="outline" className="text-xs">
            目标: {formatValue(metric.target)}
          </Badge>
        )}
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          {/* 主要数值 */}
          <div className="text-2xl font-bold">
            {formatValue(metric.value)}
            {metric.unit && <span className="text-sm font-normal text-muted-foreground ml-1">{metric.unit}</span>}
          </div>

          {/* 变化趋势 */}
          <div className="flex items-center space-x-2">
            {getTrendIcon()}
            <span className={`text-sm ${getTrendColor()}`}>
              {metric.change_percentage > 0 ? '+' : ''}{metric.change_percentage.toFixed(1)}%
            </span>
            <span className="text-sm text-muted-foreground">
              ({metric.change > 0 ? '+' : ''}{formatValue(Math.abs(metric.change))})
            </span>
          </div>

          {/* 进度条（如果有目标） */}
          {metric.target && (
            <div className="space-y-1">
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>进度</span>
                <span>{((metric.value / metric.target) * 100).toFixed(1)}%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    metric.value >= metric.target ? 'bg-green-500' : 'bg-blue-500'
                  }`}
                  style={{ width: `${Math.min((metric.value / metric.target) * 100, 100)}%` }}
                />
              </div>
            </div>
          )}

          {/* 描述 */}
          {metric.description && (
            <p className="text-xs text-muted-foreground">{metric.description}</p>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

interface KPIGridProps {
  metrics: KPIMetric[]
  className?: string
}

export function KPIGrid({ metrics, className = '' }: KPIGridProps) {
  return (
    <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {metrics.map((metric) => (
        <KPICard key={metric.id} metric={metric} />
      ))}
    </div>
  )
}

// 预定义的KPI指标
export const DEFAULT_KPI_METRICS: KPIMetric[] = [
  {
    id: 'revenue',
    name: '总收入',
    value: 125000,
    target: 150000,
    unit: '',
    format: 'currency',
    trend: 'up',
    change: 15000,
    change_percentage: 13.6,
    description: '本月总收入',
    color: 'green',
  },
  {
    id: 'orders',
    name: '订单数量',
    value: 1250,
    target: 1500,
    unit: '单',
    format: 'number',
    trend: 'up',
    change: 180,
    change_percentage: 16.8,
    description: '本月订单总数',
    color: 'blue',
  },
  {
    id: 'customers',
    name: '客户数量',
    value: 850,
    target: 1000,
    unit: '人',
    format: 'number',
    trend: 'up',
    change: 95,
    change_percentage: 12.6,
    description: '活跃客户数量',
    color: 'purple',
  },
  {
    id: 'conversion_rate',
    name: '转化率',
    value: 3.2,
    target: 4.0,
    unit: '',
    format: 'percentage',
    trend: 'down',
    change: -0.3,
    change_percentage: -8.6,
    description: '访客转化率',
    color: 'yellow',
  },
]

// 业务类型特定的KPI指标
export const BUSINESS_TYPE_KPIS = {
  hotel: [
    {
      id: 'occupancy_rate',
      name: '入住率',
      value: 78.5,
      target: 85.0,
      format: 'percentage' as const,
      trend: 'up' as const,
      change: 5.2,
      change_percentage: 7.1,
      description: '房间入住率',
    },
    {
      id: 'adr',
      name: '平均房价',
      value: 320,
      target: 350,
      format: 'currency' as const,
      trend: 'up' as const,
      change: 25,
      change_percentage: 8.5,
      description: '平均每间房收入',
    },
    {
      id: 'revpar',
      name: 'RevPAR',
      value: 251,
      target: 298,
      format: 'currency' as const,
      trend: 'up' as const,
      change: 32,
      change_percentage: 14.6,
      description: '每间可售房收入',
    },
  ],
  restaurant: [
    {
      id: 'table_turnover',
      name: '翻台率',
      value: 2.8,
      target: 3.5,
      format: 'number' as const,
      trend: 'stable' as const,
      change: 0.1,
      change_percentage: 3.7,
      description: '每日平均翻台次数',
    },
    {
      id: 'avg_check',
      name: '客单价',
      value: 85,
      target: 95,
      format: 'currency' as const,
      trend: 'up' as const,
      change: 8,
      change_percentage: 10.4,
      description: '平均每单消费',
    },
  ],
  retail: [
    {
      id: 'inventory_turnover',
      name: '库存周转率',
      value: 6.2,
      target: 8.0,
      format: 'number' as const,
      trend: 'up' as const,
      change: 0.8,
      change_percentage: 14.8,
      description: '年化库存周转次数',
    },
    {
      id: 'gross_margin',
      name: '毛利率',
      value: 42.5,
      target: 45.0,
      format: 'percentage' as const,
      trend: 'down' as const,
      change: -1.2,
      change_percentage: -2.7,
      description: '销售毛利率',
    },
  ],
}

// 获取业务类型特定的KPI
export function getBusinessTypeKPIs(businessType: string): KPIMetric[] {
  const baseKPIs = DEFAULT_KPI_METRICS
  const specificKPIs = BUSINESS_TYPE_KPIS[businessType as keyof typeof BUSINESS_TYPE_KPIS] || []
  
  return [
    ...baseKPIs,
    ...specificKPIs.map(kpi => ({
      ...kpi,
      color: 'blue' as const,
    }))
  ]
}

// KPI比较组件
interface KPIComparisonProps {
  current: KPIMetric
  previous: KPIMetric
  className?: string
}

export function KPIComparison({ current, previous, className = '' }: KPIComparisonProps) {
  const improvement = current.value - previous.value
  const improvementPercentage = previous.value > 0 ? (improvement / previous.value) * 100 : 0

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="text-sm">{current.name} - 对比分析</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <p className="text-xs text-muted-foreground">当前期间</p>
            <p className="text-lg font-bold">{current.value.toLocaleString()}</p>
          </div>
          <div>
            <p className="text-xs text-muted-foreground">上一期间</p>
            <p className="text-lg font-bold">{previous.value.toLocaleString()}</p>
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm">变化</span>
          <div className="flex items-center space-x-2">
            {improvement > 0 ? (
              <TrendingUp className="h-4 w-4 text-green-500" />
            ) : improvement < 0 ? (
              <TrendingDown className="h-4 w-4 text-red-500" />
            ) : (
              <Minus className="h-4 w-4 text-gray-500" />
            )}
            <span className={`text-sm font-medium ${
              improvement > 0 ? 'text-green-600' : 
              improvement < 0 ? 'text-red-600' : 'text-gray-600'
            }`}>
              {improvement > 0 ? '+' : ''}{improvement.toLocaleString()} 
              ({improvementPercentage > 0 ? '+' : ''}{improvementPercentage.toFixed(1)}%)
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
