/**
 * 认证表单验证模式
 * 使用 Zod 进行表单数据验证
 */

import { z } from 'zod'

// 登录表单验证模式
export const loginSchema = z.object({
  email: z
    .string()
    .min(1, '请输入邮箱地址')
    .email('请输入有效的邮箱地址'),
  password: z
    .string()
    .min(1, '请输入密码')
    .min(6, '密码至少需要6个字符'),
})

// 注册表单验证模式
export const registerSchema = z.object({
  email: z
    .string()
    .min(1, '请输入邮箱地址')
    .email('请输入有效的邮箱地址'),
  password: z
    .string()
    .min(1, '请输入密码')
    .min(6, '密码至少需要6个字符')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      '密码必须包含至少一个大写字母、一个小写字母和一个数字'
    ),
  confirmPassword: z
    .string()
    .min(1, '请确认密码'),
  name: z
    .string()
    .min(1, '请输入姓名')
    .min(2, '姓名至少需要2个字符')
    .max(50, '姓名不能超过50个字符'),
  merchantName: z
    .string()
    .min(1, '请输入商家名称')
    .min(2, '商家名称至少需要2个字符')
    .max(100, '商家名称不能超过100个字符'),
  businessTypes: z
    .array(z.enum(['hotel', 'restaurant', 'product']))
    .min(1, '请至少选择一种业务类型'),
  phone: z
    .string()
    .optional()
    .refine((val) => !val || /^1[3-9]\d{9}$/.test(val), {
      message: '请输入有效的手机号码',
    }),
  agreeToTerms: z
    .boolean()
    .refine((val) => val === true, {
      message: '请同意服务条款和隐私政策',
    }),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
})

// 忘记密码表单验证模式
export const forgotPasswordSchema = z.object({
  email: z
    .string()
    .min(1, '请输入邮箱地址')
    .email('请输入有效的邮箱地址'),
})

// 重置密码表单验证模式
export const resetPasswordSchema = z.object({
  password: z
    .string()
    .min(1, '请输入新密码')
    .min(6, '密码至少需要6个字符')
    .regex(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
      '密码必须包含至少一个大写字母、一个小写字母和一个数字'
    ),
  confirmPassword: z
    .string()
    .min(1, '请确认新密码'),
}).refine((data) => data.password === data.confirmPassword, {
  message: '两次输入的密码不一致',
  path: ['confirmPassword'],
})

// 更新个人资料表单验证模式
export const updateProfileSchema = z.object({
  name: z
    .string()
    .min(1, '请输入姓名')
    .min(2, '姓名至少需要2个字符')
    .max(50, '姓名不能超过50个字符'),
  phone: z
    .string()
    .optional()
    .refine((val) => !val || /^1[3-9]\d{9}$/.test(val), {
      message: '请输入有效的手机号码',
    }),
})

// 类型导出
export type LoginFormData = z.infer<typeof loginSchema>
export type RegisterFormData = z.infer<typeof registerSchema>
export type ForgotPasswordFormData = z.infer<typeof forgotPasswordSchema>
export type ResetPasswordFormData = z.infer<typeof resetPasswordSchema>
export type UpdateProfileFormData = z.infer<typeof updateProfileSchema>
