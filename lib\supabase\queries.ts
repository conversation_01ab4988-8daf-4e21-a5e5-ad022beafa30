/**
 * Supabase 数据库查询工具函数
 * 提供常用的数据库操作封装
 */

import { createClient } from './client'
import { createClient as createServerClient } from './server'
import type { Database } from '@/types/database'

// 类型别名
type Tables = Database['public']['Tables']
type Merchant = Tables['merchants']['Row']
type Hotel = Tables['hotels']['Row']
type Restaurant = Tables['restaurants']['Row']
type Product = Tables['products']['Row']
type Order = Tables['orders']['Row']
type Payment = Tables['payments']['Row']

/**
 * 商家相关查询
 */
export class MerchantQueries {
  private supabase = createClient()

  // 获取当前用户的商家信息
  async getCurrentMerchant(userId: string): Promise<Merchant | null> {
    const { data, error } = await this.supabase
      .from('merchants')
      .select('*')
      .eq('user_id', userId)
      .single()

    if (error) {
      console.error('Error fetching merchant:', error)
      return null
    }

    return data
  }

  // 创建商家
  async createMerchant(merchantData: Tables['merchants']['Insert']): Promise<Merchant | null> {
    const { data, error } = await this.supabase
      .from('merchants')
      .insert(merchantData)
      .select()
      .single()

    if (error) {
      console.error('Error creating merchant:', error)
      return null
    }

    return data
  }

  // 更新商家信息
  async updateMerchant(id: string, updates: Tables['merchants']['Update']): Promise<Merchant | null> {
    const { data, error } = await this.supabase
      .from('merchants')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating merchant:', error)
      return null
    }

    return data
  }

  // 获取商家统计数据
  async getMerchantStats(merchantId: string) {
    const { data, error } = await this.supabase
      .rpc('get_merchant_stats', { merchant_uuid: merchantId })

    if (error) {
      console.error('Error fetching merchant stats:', error)
      return null
    }

    return data
  }
}

/**
 * 酒店相关查询
 */
export class HotelQueries {
  private supabase = createClient()

  // 获取商家的所有酒店
  async getHotelsByMerchant(merchantId: string): Promise<Hotel[]> {
    const { data, error } = await this.supabase
      .from('hotels')
      .select('*')
      .eq('merchant_id', merchantId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching hotels:', error)
      return []
    }

    return data || []
  }

  // 获取酒店详情
  async getHotelById(id: string): Promise<Hotel | null> {
    const { data, error } = await this.supabase
      .from('hotels')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching hotel:', error)
      return null
    }

    return data
  }

  // 创建酒店
  async createHotel(hotelData: Tables['hotels']['Insert']): Promise<Hotel | null> {
    const { data, error } = await this.supabase
      .from('hotels')
      .insert(hotelData)
      .select()
      .single()

    if (error) {
      console.error('Error creating hotel:', error)
      return null
    }

    return data
  }

  // 更新酒店信息
  async updateHotel(id: string, updates: Tables['hotels']['Update']): Promise<Hotel | null> {
    const { data, error } = await this.supabase
      .from('hotels')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating hotel:', error)
      return null
    }

    return data
  }

  // 删除酒店
  async deleteHotel(id: string): Promise<boolean> {
    const { error } = await this.supabase
      .from('hotels')
      .delete()
      .eq('id', id)

    if (error) {
      console.error('Error deleting hotel:', error)
      return false
    }

    return true
  }
}

/**
 * 餐厅相关查询
 */
export class RestaurantQueries {
  private supabase = createClient()

  // 获取商家的所有餐厅
  async getRestaurantsByMerchant(merchantId: string): Promise<Restaurant[]> {
    const { data, error } = await this.supabase
      .from('restaurants')
      .select('*')
      .eq('merchant_id', merchantId)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching restaurants:', error)
      return []
    }

    return data || []
  }

  // 获取餐厅详情
  async getRestaurantById(id: string): Promise<Restaurant | null> {
    const { data, error } = await this.supabase
      .from('restaurants')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching restaurant:', error)
      return null
    }

    return data
  }

  // 创建餐厅
  async createRestaurant(restaurantData: Tables['restaurants']['Insert']): Promise<Restaurant | null> {
    const { data, error } = await this.supabase
      .from('restaurants')
      .insert(restaurantData)
      .select()
      .single()

    if (error) {
      console.error('Error creating restaurant:', error)
      return null
    }

    return data
  }
}

/**
 * 商品相关查询
 */
export class ProductQueries {
  private supabase = createClient()

  // 搜索商品
  async searchProducts(
    merchantId: string,
    searchQuery: string = '',
    categoryId?: string,
    limit: number = 20,
    offset: number = 0
  ) {
    const { data, error } = await this.supabase
      .rpc('search_products', {
        merchant_uuid: merchantId,
        search_query: searchQuery,
        category_uuid: categoryId,
        limit_count: limit,
        offset_count: offset
      })

    if (error) {
      console.error('Error searching products:', error)
      return []
    }

    return data || []
  }

  // 获取低库存商品
  async getLowStockProducts(merchantId: string): Promise<Product[]> {
    const { data, error } = await this.supabase
      .from('products')
      .select('*')
      .eq('merchant_id', merchantId)
      .filter('stock_quantity', 'lte', 'min_stock_level')
      .eq('is_active', true)

    if (error) {
      console.error('Error fetching low stock products:', error)
      return []
    }

    return data || []
  }
}

/**
 * 订单相关查询
 */
export class OrderQueries {
  private supabase = createClient()

  // 获取商家的订单列表
  async getOrdersByMerchant(
    merchantId: string,
    status?: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<Order[]> {
    let query = this.supabase
      .from('orders')
      .select('*')
      .eq('merchant_id', merchantId)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1)

    if (status) {
      query = query.eq('status', status)
    }

    const { data, error } = await query

    if (error) {
      console.error('Error fetching orders:', error)
      return []
    }

    return data || []
  }

  // 获取订单详情
  async getOrderById(id: string): Promise<Order | null> {
    const { data, error } = await this.supabase
      .from('orders')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      console.error('Error fetching order:', error)
      return null
    }

    return data
  }

  // 更新订单状态
  async updateOrderStatus(id: string, status: string): Promise<Order | null> {
    const { data, error } = await this.supabase
      .from('orders')
      .update({ status: status as any })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating order status:', error)
      return null
    }

    return data
  }
}

/**
 * 支付相关查询
 */
export class PaymentQueries {
  private supabase = createClient()

  // 获取订单的支付记录
  async getPaymentsByOrder(orderId: string): Promise<Payment[]> {
    const { data, error } = await this.supabase
      .from('payments')
      .select('*')
      .eq('order_id', orderId)
      .order('created_at', { ascending: false })

    if (error) {
      console.error('Error fetching payments:', error)
      return []
    }

    return data || []
  }

  // 创建支付记录
  async createPayment(paymentData: Tables['payments']['Insert']): Promise<Payment | null> {
    const { data, error } = await this.supabase
      .from('payments')
      .insert(paymentData)
      .select()
      .single()

    if (error) {
      console.error('Error creating payment:', error)
      return null
    }

    return data
  }

  // 更新支付状态
  async updatePaymentStatus(id: string, status: string, transactionId?: string): Promise<Payment | null> {
    const updates: any = { status }
    if (transactionId) {
      updates.transaction_id = transactionId
    }
    if (status === 'success') {
      updates.paid_at = new Date().toISOString()
    }

    const { data, error } = await this.supabase
      .from('payments')
      .update(updates)
      .eq('id', id)
      .select()
      .single()

    if (error) {
      console.error('Error updating payment status:', error)
      return null
    }

    return data
  }
}

// 导出查询类实例
export const merchantQueries = new MerchantQueries()
export const hotelQueries = new HotelQueries()
export const restaurantQueries = new RestaurantQueries()
export const productQueries = new ProductQueries()
export const orderQueries = new OrderQueries()
export const paymentQueries = new PaymentQueries()
