/**
 * Supabase 浏览器端客户端配置
 * 用于客户端组件和浏览器环境
 */

import { createBrowserClient } from '@supabase/ssr'
import type { Database } from '@/types/database'

// 创建浏览器端 Supabase 客户端
export const createClient = () => {
  return createBrowserClient<Database>(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

// 导出默认客户端实例
export const supabase = createClient()

// 类型导出
export type SupabaseClient = ReturnType<typeof createClient>
