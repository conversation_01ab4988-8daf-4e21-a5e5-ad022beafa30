/**
 * 支付相关类型定义
 * 统一的支付系统类型
 */

import type { Database } from './database'

// 从数据库类型中提取支付相关类型
export type Payment = Database['public']['Tables']['payments']['Row']
export type PaymentInsert = Database['public']['Tables']['payments']['Insert']
export type PaymentUpdate = Database['public']['Tables']['payments']['Update']

// 支付状态枚举
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded'

// 支付方式枚举
export type PaymentMethod = 'qiling_pay' | 'alipay' | 'wechat_pay' | 'bank_card' | 'cash'

// 支付类型枚举
export type PaymentType = 'order' | 'refund' | 'transfer'

// 货币类型
export type Currency = 'CNY' | 'USD' | 'EUR'

// 支付提供商接口
export interface PaymentProvider {
  name: string
  displayName: string
  icon: string
  isEnabled: boolean
  config: Record<string, any>
  
  // 核心方法
  createPayment(params: CreatePaymentParams): Promise<PaymentResult>
  queryPayment(paymentId: string): Promise<PaymentQueryResult>
  cancelPayment(paymentId: string): Promise<PaymentResult>
  refundPayment(paymentId: string, amount?: number): Promise<PaymentResult>
  verifyCallback(data: any): Promise<CallbackVerifyResult>
}

// 创建支付参数
export interface CreatePaymentParams {
  orderId: string
  amount: number
  currency: Currency
  description: string
  customerId?: string
  customerName?: string
  customerPhone?: string
  customerEmail?: string
  returnUrl?: string
  notifyUrl?: string
  metadata?: Record<string, any>
}

// 支付结果
export interface PaymentResult {
  success: boolean
  paymentId?: string
  paymentUrl?: string
  qrCode?: string
  error?: string
  message?: string
  data?: any
}

// 支付查询结果
export interface PaymentQueryResult {
  success: boolean
  status: PaymentStatus
  amount?: number
  paidAmount?: number
  paidAt?: string
  error?: string
  data?: any
}

// 回调验证结果
export interface CallbackVerifyResult {
  success: boolean
  paymentId?: string
  status?: PaymentStatus
  amount?: number
  error?: string
  data?: any
}

// 支付详细信息类型
export interface PaymentWithDetails extends Payment {
  order?: {
    id: string
    order_number: string
    customer_name: string
    order_type: string
  }
  refunds?: PaymentRefund[]
}

// 退款信息类型
export interface PaymentRefund {
  id: string
  payment_id: string
  amount: number
  reason?: string
  status: 'pending' | 'completed' | 'failed'
  created_at: string
  completed_at?: string
}

// 支付搜索参数类型
export interface PaymentSearchParams {
  query?: string
  status?: PaymentStatus[]
  method?: PaymentMethod[]
  type?: PaymentType[]
  date_from?: string
  date_to?: string
  min_amount?: number
  max_amount?: number
  order_id?: string
  customer_name?: string
  sort_by?: 'created_at' | 'amount' | 'payment_id'
  sort_order?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// 支付统计信息类型
export interface PaymentStats {
  total_payments: number
  completed_payments: number
  failed_payments: number
  pending_payments: number
  total_amount: number
  completed_amount: number
  refunded_amount: number
  average_payment_amount: number
  payments_by_method: Record<PaymentMethod, number>
  revenue_by_method: Record<PaymentMethod, number>
  daily_revenue: Array<{
    date: string
    amount: number
    count: number
  }>
}

// 支付操作结果类型
export interface PaymentActionResult {
  success: boolean
  data?: Payment | PaymentWithDetails
  error?: string
  message?: string
}

// 支付状态配置
export const PAYMENT_STATUS_CONFIG = {
  pending: {
    label: '待支付',
    description: '等待用户支付',
    color: 'yellow',
    icon: '⏳',
  },
  processing: {
    label: '处理中',
    description: '支付处理中',
    color: 'blue',
    icon: '🔄',
  },
  completed: {
    label: '支付成功',
    description: '支付已完成',
    color: 'green',
    icon: '✅',
  },
  failed: {
    label: '支付失败',
    description: '支付失败',
    color: 'red',
    icon: '❌',
  },
  cancelled: {
    label: '已取消',
    description: '支付已取消',
    color: 'gray',
    icon: '🚫',
  },
  refunded: {
    label: '已退款',
    description: '支付已退款',
    color: 'purple',
    icon: '💰',
  },
} as const

// 支付方式配置
export const PAYMENT_METHOD_CONFIG = {
  qiling_pay: {
    label: '柒零支付',
    description: '内部支付系统',
    icon: '💳',
    color: 'blue',
    isEnabled: true,
    features: ['instant', 'refund', 'query'],
  },
  alipay: {
    label: '支付宝',
    description: '支付宝支付',
    icon: '🔵',
    color: 'blue',
    isEnabled: true,
    features: ['qr_code', 'web', 'refund', 'query'],
  },
  wechat_pay: {
    label: '微信支付',
    description: '微信支付',
    icon: '🟢',
    color: 'green',
    isEnabled: true,
    features: ['qr_code', 'native', 'refund', 'query'],
  },
  bank_card: {
    label: '银行卡',
    description: '银行卡支付',
    icon: '💳',
    color: 'gray',
    isEnabled: false,
    features: ['web', 'secure'],
  },
  cash: {
    label: '现金支付',
    description: '线下现金支付',
    icon: '💵',
    color: 'green',
    isEnabled: true,
    features: ['offline', 'manual'],
  },
} as const

// 支付类型配置
export const PAYMENT_TYPE_CONFIG = {
  order: {
    label: '订单支付',
    description: '订单相关支付',
    icon: '🛒',
  },
  refund: {
    label: '退款',
    description: '退款操作',
    icon: '💰',
  },
  transfer: {
    label: '转账',
    description: '资金转账',
    icon: '💸',
  },
} as const

// 柒零支付配置
export interface QilingPayConfig {
  apiUrl: string
  merchantId: string
  secretKey: string
  publicKey: string
  notifyUrl: string
  returnUrl: string
  timeout: number
}

// 柒零支付API响应
export interface QilingPayResponse {
  code: number
  message: string
  data?: any
  timestamp: number
  signature: string
}

// 柒零支付创建订单参数
export interface QilingPayCreateOrderParams {
  merchant_id: string
  order_id: string
  amount: number
  currency: string
  description: string
  customer_id?: string
  notify_url: string
  return_url: string
  timestamp: number
  signature: string
}

// 柒零支付订单状态
export interface QilingPayOrderStatus {
  order_id: string
  payment_id: string
  status: string
  amount: number
  paid_amount: number
  paid_at?: string
  created_at: string
}
