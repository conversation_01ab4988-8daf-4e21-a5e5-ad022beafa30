/**
 * Logo上传组件
 * 用于上传和管理商家Logo
 */

'use client'

import { useState, useRef } from 'react'
import Image from 'next/image'
import { Upload, X, Camera } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { uploadMerchantLogo } from '@/lib/actions/merchant'

interface LogoUploadProps {
  currentLogo?: string
  onSuccess?: (logoUrl: string) => void
  className?: string
}

export function LogoUpload({ currentLogo, onSuccess, className }: LogoUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [previewUrl, setPreviewUrl] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      setError('只支持 JPEG、PNG、WebP 格式的图片')
      return
    }

    // 验证文件大小
    if (file.size > 5 * 1024 * 1024) {
      setError('图片大小不能超过5MB')
      return
    }

    // 创建预览URL
    const url = URL.createObjectURL(file)
    setPreviewUrl(url)
    setError('')

    // 自动上传
    handleUpload(file)
  }

  const handleUpload = async (file: File) => {
    setIsUploading(true)
    setError('')
    setSuccess('')

    try {
      const formData = new FormData()
      formData.append('file', file)

      const result = await uploadMerchantLogo(formData)

      if (result.success && result.data) {
        setSuccess(result.message || 'Logo上传成功')
        onSuccess?.(result.data.logo || '')
      } else {
        setError(result.error || '上传失败')
        setPreviewUrl(null)
      }
    } catch (err) {
      setError('上传失败，请稍后重试')
      setPreviewUrl(null)
    } finally {
      setIsUploading(false)
    }
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  const handleRemovePreview = () => {
    setPreviewUrl(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const displayUrl = previewUrl || currentLogo

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>商家Logo</CardTitle>
        <CardDescription>
          上传您的商家Logo，支持 JPEG、PNG、WebP 格式，文件大小不超过5MB
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* 错误提示 */}
        {error && (
          <div className="mb-4 p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}

        {/* 成功提示 */}
        {success && (
          <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
            <p className="text-sm text-green-600">{success}</p>
          </div>
        )}

        <div className="space-y-4">
          {/* Logo预览区域 */}
          <div className="flex items-center justify-center">
            {displayUrl ? (
              <div className="relative group">
                <div className="relative w-32 h-32 rounded-lg overflow-hidden border-2 border-border">
                  <Image
                    src={displayUrl}
                    alt="商家Logo"
                    fill
                    className="object-cover"
                  />
                  {isUploading && (
                    <div className="absolute inset-0 bg-black/50 flex items-center justify-center">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-white"></div>
                    </div>
                  )}
                </div>
                
                {previewUrl && !isUploading && (
                  <Button
                    type="button"
                    variant="destructive"
                    size="icon"
                    className="absolute -top-2 -right-2 h-6 w-6 rounded-full"
                    onClick={handleRemovePreview}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                )}
              </div>
            ) : (
              <div
                className="w-32 h-32 border-2 border-dashed border-border rounded-lg flex flex-col items-center justify-center cursor-pointer hover:border-primary/50 transition-colors"
                onClick={handleClick}
              >
                <Camera className="h-8 w-8 text-muted-foreground mb-2" />
                <span className="text-sm text-muted-foreground text-center">
                  点击上传Logo
                </span>
              </div>
            )}
          </div>

          {/* 上传按钮 */}
          <div className="flex justify-center">
            <Button
              type="button"
              variant="outline"
              onClick={handleClick}
              disabled={isUploading}
              className="flex items-center space-x-2"
            >
              <Upload className="h-4 w-4" />
              <span>{displayUrl ? '更换Logo' : '上传Logo'}</span>
            </Button>
          </div>

          {/* 隐藏的文件输入 */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/jpeg,image/png,image/webp"
            onChange={handleFileSelect}
            className="hidden"
            disabled={isUploading}
          />

          {/* 上传说明 */}
          <div className="text-xs text-muted-foreground text-center space-y-1">
            <p>• 建议尺寸：200x200像素</p>
            <p>• 支持格式：JPEG、PNG、WebP</p>
            <p>• 文件大小：不超过5MB</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
