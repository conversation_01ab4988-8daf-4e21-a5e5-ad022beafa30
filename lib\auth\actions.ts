/**
 * 认证相关的 Server Actions
 * 处理用户注册、登录、登出等操作
 */

'use server'

import { revalidatePath } from 'next/cache'
import { redirect } from 'next/navigation'
import { createClient } from '@/lib/supabase/server'
import { merchantQueries } from '@/lib/supabase/queries'
import type { LoginFormData, RegisterFormData } from '@/lib/validations/auth'

// 登录操作
export async function login(formData: LoginFormData) {
  const supabase = createClient()

  try {
    const { error } = await supabase.auth.signInWithPassword({
      email: formData.email,
      password: formData.password,
    })

    if (error) {
      return {
        success: false,
        error: getAuthErrorMessage(error.message),
      }
    }

    revalidatePath('/', 'layout')
    return {
      success: true,
      message: '登录成功',
    }
  } catch (error) {
    console.error('Login error:', error)
    return {
      success: false,
      error: '登录失败，请稍后重试',
    }
  }
}

// 注册操作
export async function register(formData: RegisterFormData) {
  const supabase = createClient()

  try {
    // 1. 创建用户账户
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email: formData.email,
      password: formData.password,
      options: {
        data: {
          name: formData.name,
          phone: formData.phone || null,
        },
      },
    })

    if (authError) {
      return {
        success: false,
        error: getAuthErrorMessage(authError.message),
      }
    }

    if (!authData.user) {
      return {
        success: false,
        error: '注册失败，请稍后重试',
      }
    }

    // 2. 创建商家记录
    const merchantData = {
      user_id: authData.user.id,
      name: formData.merchantName,
      business_types: formData.businessTypes,
      contact_info: {
        email: formData.email,
        phone: formData.phone || null,
      },
      status: 'active' as const,
    }

    const merchant = await merchantQueries.createMerchant(merchantData)

    if (!merchant) {
      // 如果商家创建失败，需要清理已创建的用户
      await supabase.auth.admin.deleteUser(authData.user.id)
      return {
        success: false,
        error: '商家信息创建失败，请稍后重试',
      }
    }

    return {
      success: true,
      message: '注册成功！请检查您的邮箱以验证账户。',
      needsVerification: !authData.user.email_confirmed_at,
    }
  } catch (error) {
    console.error('Register error:', error)
    return {
      success: false,
      error: '注册失败，请稍后重试',
    }
  }
}

// 登出操作
export async function logout() {
  const supabase = createClient()

  try {
    const { error } = await supabase.auth.signOut()

    if (error) {
      return {
        success: false,
        error: '登出失败，请稍后重试',
      }
    }

    revalidatePath('/', 'layout')
    return {
      success: true,
      message: '已成功登出',
    }
  } catch (error) {
    console.error('Logout error:', error)
    return {
      success: false,
      error: '登出失败，请稍后重试',
    }
  }
}

// 发送密码重置邮件
export async function forgotPassword(email: string) {
  const supabase = createClient()

  try {
    const { error } = await supabase.auth.resetPasswordForEmail(email, {
      redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/reset-password`,
    })

    if (error) {
      return {
        success: false,
        error: getAuthErrorMessage(error.message),
      }
    }

    return {
      success: true,
      message: '密码重置邮件已发送，请检查您的邮箱',
    }
  } catch (error) {
    console.error('Forgot password error:', error)
    return {
      success: false,
      error: '发送重置邮件失败，请稍后重试',
    }
  }
}

// 重置密码
export async function resetPassword(password: string) {
  const supabase = createClient()

  try {
    const { error } = await supabase.auth.updateUser({
      password: password,
    })

    if (error) {
      return {
        success: false,
        error: getAuthErrorMessage(error.message),
      }
    }

    revalidatePath('/', 'layout')
    return {
      success: true,
      message: '密码重置成功',
    }
  } catch (error) {
    console.error('Reset password error:', error)
    return {
      success: false,
      error: '密码重置失败，请稍后重试',
    }
  }
}

// 获取当前用户信息
export async function getCurrentUser() {
  const supabase = createClient()

  try {
    const { data: { user }, error } = await supabase.auth.getUser()

    if (error || !user) {
      return null
    }

    // 获取商家信息
    const merchant = await merchantQueries.getCurrentMerchant(user.id)

    return {
      id: user.id,
      email: user.email!,
      name: user.user_metadata?.name || '',
      phone: user.user_metadata?.phone || '',
      emailConfirmed: !!user.email_confirmed_at,
      createdAt: user.created_at,
      merchant,
    }
  } catch (error) {
    console.error('Get current user error:', error)
    return null
  }
}

// 重新发送验证邮件
export async function resendVerificationEmail() {
  const supabase = createClient()

  try {
    const { data: { user } } = await supabase.auth.getUser()

    if (!user) {
      return {
        success: false,
        error: '用户未登录',
      }
    }

    const { error } = await supabase.auth.resend({
      type: 'signup',
      email: user.email!,
    })

    if (error) {
      return {
        success: false,
        error: getAuthErrorMessage(error.message),
      }
    }

    return {
      success: true,
      message: '验证邮件已重新发送',
    }
  } catch (error) {
    console.error('Resend verification email error:', error)
    return {
      success: false,
      error: '发送验证邮件失败，请稍后重试',
    }
  }
}

// 错误消息转换
function getAuthErrorMessage(error: string): string {
  const errorMessages: Record<string, string> = {
    'Invalid login credentials': '邮箱或密码错误',
    'Email not confirmed': '请先验证您的邮箱地址',
    'User already registered': '该邮箱已被注册',
    'Password should be at least 6 characters': '密码至少需要6个字符',
    'Unable to validate email address: invalid format': '邮箱格式无效',
    'Signup is disabled': '注册功能已禁用',
    'Email rate limit exceeded': '邮件发送频率过高，请稍后重试',
    'Token has expired or is invalid': '链接已过期或无效',
  }

  return errorMessages[error] || error || '操作失败，请稍后重试'
}
