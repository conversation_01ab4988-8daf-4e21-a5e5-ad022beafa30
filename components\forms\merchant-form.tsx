/**
 * 商家信息表单组件
 * 用于编辑商家基础信息
 */

'use client'

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { merchantUpdateSchema, defaultBusinessHours } from '@/lib/validations/merchant'
import { updateMerchant } from '@/lib/actions/merchant'
import { 
  BUSINESS_TYPE_CONFIG, 
  WEEKDAY_CONFIG,
  type MerchantUpdateFormData,
  type Merchant,
  type BusinessType,
  type WeekDay
} from '@/types/merchant'

interface MerchantFormProps {
  merchant: Merchant
  onSuccess?: () => void
}

export function MerchantForm({ merchant, onSuccess }: MerchantFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
    setValue,
  } = useForm<MerchantUpdateFormData>({
    resolver: zodResolver(merchantUpdateSchema),
    defaultValues: {
      name: merchant.name,
      description: merchant.description || '',
      business_types: merchant.business_types || [],
      contact_info: {
        phone: (merchant.contact_info as any)?.phone || '',
        email: (merchant.contact_info as any)?.email || '',
        address: (merchant.contact_info as any)?.address || '',
        website: (merchant.contact_info as any)?.website || '',
      },
      business_hours: (merchant.business_hours as any) || defaultBusinessHours,
      logo: merchant.logo || '',
    },
  })

  const businessTypes = watch('business_types')
  const businessHours = watch('business_hours')

  const handleBusinessTypeChange = (type: BusinessType, checked: boolean) => {
    const currentTypes = businessTypes || []
    const newTypes = checked
      ? [...currentTypes, type]
      : currentTypes.filter(t => t !== type)
    
    setValue('business_types', newTypes)
  }

  const handleBusinessHourChange = (day: WeekDay, field: 'open' | 'close' | 'isOpen', value: string | boolean) => {
    const currentHours = businessHours || defaultBusinessHours
    setValue('business_hours', {
      ...currentHours,
      [day]: {
        ...currentHours[day],
        [field]: value,
      },
    })
  }

  const onSubmit = async (data: MerchantUpdateFormData) => {
    setIsLoading(true)
    setError('')
    setSuccess('')

    try {
      const result = await updateMerchant(data)

      if (result.success) {
        setSuccess(result.message || '商家信息更新成功')
        onSuccess?.()
      } else {
        setError(result.error || '更新失败')
      }
    } catch (err) {
      setError('更新失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* 错误提示 */}
      {error && (
        <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-md">
          <p className="text-sm text-destructive">{error}</p>
        </div>
      )}

      {/* 成功提示 */}
      {success && (
        <div className="p-4 bg-green-50 border border-green-200 rounded-md">
          <p className="text-sm text-green-600">{success}</p>
        </div>
      )}

      {/* 基础信息 */}
      <Card>
        <CardHeader>
          <CardTitle>基础信息</CardTitle>
          <CardDescription>编辑您的商家基础信息</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <label htmlFor="name" className="block text-sm font-medium mb-2">
              商家名称 *
            </label>
            <Input
              {...register('name')}
              id="name"
              placeholder="请输入商家名称"
              disabled={isLoading}
              className={errors.name ? 'border-destructive' : ''}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-destructive">{errors.name.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="description" className="block text-sm font-medium mb-2">
              商家描述
            </label>
            <textarea
              {...register('description')}
              id="description"
              rows={3}
              placeholder="请输入商家描述"
              disabled={isLoading}
              className="w-full px-3 py-2 border border-input rounded-md shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
            />
            {errors.description && (
              <p className="mt-1 text-sm text-destructive">{errors.description.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 业务类型 */}
      <Card>
        <CardHeader>
          <CardTitle>业务类型</CardTitle>
          <CardDescription>选择您的商家经营的业务类型</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {Object.entries(BUSINESS_TYPE_CONFIG).map(([type, config]) => (
              <div
                key={type}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  businessTypes?.includes(type as BusinessType)
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-primary/50'
                }`}
                onClick={() => handleBusinessTypeChange(
                  type as BusinessType,
                  !businessTypes?.includes(type as BusinessType)
                )}
              >
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={businessTypes?.includes(type as BusinessType) || false}
                    onChange={(e) => handleBusinessTypeChange(type as BusinessType, e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                    disabled={isLoading}
                  />
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{config.icon}</span>
                      <span className="font-medium">{config.label}</span>
                    </div>
                    <p className="text-sm text-muted-foreground mt-1">
                      {config.description}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
          {errors.business_types && (
            <p className="mt-2 text-sm text-destructive">{errors.business_types.message}</p>
          )}
        </CardContent>
      </Card>

      {/* 联系信息 */}
      <Card>
        <CardHeader>
          <CardTitle>联系信息</CardTitle>
          <CardDescription>设置您的商家联系方式</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label htmlFor="phone" className="block text-sm font-medium mb-2">
                联系电话
              </label>
              <Input
                {...register('contact_info.phone')}
                id="phone"
                type="tel"
                placeholder="请输入联系电话"
                disabled={isLoading}
                className={errors.contact_info?.phone ? 'border-destructive' : ''}
              />
              {errors.contact_info?.phone && (
                <p className="mt-1 text-sm text-destructive">{errors.contact_info.phone.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-2">
                联系邮箱
              </label>
              <Input
                {...register('contact_info.email')}
                id="email"
                type="email"
                placeholder="请输入联系邮箱"
                disabled={isLoading}
                className={errors.contact_info?.email ? 'border-destructive' : ''}
              />
              {errors.contact_info?.email && (
                <p className="mt-1 text-sm text-destructive">{errors.contact_info.email.message}</p>
              )}
            </div>
          </div>

          <div>
            <label htmlFor="address" className="block text-sm font-medium mb-2">
              商家地址
            </label>
            <Input
              {...register('contact_info.address')}
              id="address"
              placeholder="请输入商家地址"
              disabled={isLoading}
              className={errors.contact_info?.address ? 'border-destructive' : ''}
            />
            {errors.contact_info?.address && (
              <p className="mt-1 text-sm text-destructive">{errors.contact_info.address.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="website" className="block text-sm font-medium mb-2">
              官方网站
            </label>
            <Input
              {...register('contact_info.website')}
              id="website"
              type="url"
              placeholder="请输入官方网站地址"
              disabled={isLoading}
              className={errors.contact_info?.website ? 'border-destructive' : ''}
            />
            {errors.contact_info?.website && (
              <p className="mt-1 text-sm text-destructive">{errors.contact_info.website.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 营业时间 */}
      <Card>
        <CardHeader>
          <CardTitle>营业时间</CardTitle>
          <CardDescription>设置您的商家营业时间</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(WEEKDAY_CONFIG).map(([day, label]) => (
              <div key={day} className="flex items-center space-x-4">
                <div className="w-16">
                  <span className="text-sm font-medium">{label}</span>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={businessHours?.[day as WeekDay]?.isOpen || false}
                    onChange={(e) => handleBusinessHourChange(day as WeekDay, 'isOpen', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                    disabled={isLoading}
                  />
                  <span className="text-sm">营业</span>
                </div>

                {businessHours?.[day as WeekDay]?.isOpen && (
                  <>
                    <Input
                      type="time"
                      value={businessHours[day as WeekDay]?.open || '09:00'}
                      onChange={(e) => handleBusinessHourChange(day as WeekDay, 'open', e.target.value)}
                      disabled={isLoading}
                      className="w-32"
                    />
                    <span className="text-sm text-muted-foreground">至</span>
                    <Input
                      type="time"
                      value={businessHours[day as WeekDay]?.close || '18:00'}
                      onChange={(e) => handleBusinessHourChange(day as WeekDay, 'close', e.target.value)}
                      disabled={isLoading}
                      className="w-32"
                    />
                  </>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 提交按钮 */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? '保存中...' : '保存更改'}
        </Button>
      </div>
    </form>
  )
}
