/**
 * 商品相关类型定义
 * 扩展数据库类型，添加商品业务逻辑类型
 */

import type { Database } from './database'

// 从数据库类型中提取商品相关类型
export type Product = Database['public']['Tables']['products']['Row']
export type ProductInsert = Database['public']['Tables']['products']['Insert']
export type ProductUpdate = Database['public']['Tables']['products']['Update']

export type ProductCategory = Database['public']['Tables']['product_categories']['Row']
export type ProductCategoryInsert = Database['public']['Tables']['product_categories']['Insert']
export type ProductCategoryUpdate = Database['public']['Tables']['product_categories']['Update']

export type ProductVariant = Database['public']['Tables']['product_variants']['Row']
export type ProductVariantInsert = Database['public']['Tables']['product_variants']['Insert']
export type ProductVariantUpdate = Database['public']['Tables']['product_variants']['Update']

// 商品状态枚举
export type ProductStatus = 'active' | 'inactive' | 'out_of_stock' | 'discontinued'

// 库存状态枚举
export type StockStatus = 'in_stock' | 'low_stock' | 'out_of_stock' | 'pre_order'

// 商品类型枚举
export const PRODUCT_TYPES = [
  'physical', 'digital', 'service', 'subscription', 'gift_card', 'bundle'
] as const

export type ProductType = typeof PRODUCT_TYPES[number]

// 商品分类枚举
export const PRODUCT_CATEGORIES = [
  'electronics', 'clothing', 'food', 'books', 'home', 'beauty',
  'sports', 'toys', 'automotive', 'health', 'jewelry', 'art',
  'music', 'software', 'services', 'other'
] as const

export type ProductCategoryType = typeof PRODUCT_CATEGORIES[number]

// 商品属性类型
export interface ProductAttribute {
  name: string
  value: string
  type: 'text' | 'number' | 'boolean' | 'select' | 'color' | 'size'
  options?: string[]
  required?: boolean
}

// 商品规格类型
export interface ProductSpecification {
  name: string
  value: string
  unit?: string
  group?: string
}

// 库存信息类型
export interface StockInfo {
  quantity: number
  reserved_quantity?: number
  available_quantity: number
  reorder_point?: number
  max_stock?: number
  location?: string
  batch_number?: string
  expiry_date?: string
}

// 价格信息类型
export interface PriceInfo {
  base_price: number
  sale_price?: number
  cost_price?: number
  wholesale_price?: number
  currency: string
  tax_rate?: number
  discount_percentage?: number
  price_tiers?: Array<{
    min_quantity: number
    price: number
  }>
}

// 商品详细信息类型（包含关联数据）
export interface ProductWithDetails extends Product {
  category?: ProductCategory
  variants?: ProductVariantWithDetails[]
  total_sales?: number
  revenue?: number
  stock_info?: StockInfo
  price_info?: PriceInfo
  reviews_count?: number
  average_rating?: number
}

// 商品变体详细信息类型
export interface ProductVariantWithDetails extends ProductVariant {
  product?: Product
  stock_info?: StockInfo
  sales_count?: number
}

// 商品分类详细信息类型
export interface ProductCategoryWithDetails extends ProductCategory {
  products?: ProductWithDetails[]
  products_count?: number
  subcategories?: ProductCategoryWithDetails[]
}

// 商品表单数据类型
export interface ProductFormData {
  name: string
  description?: string
  short_description?: string
  category_id?: string
  product_type: ProductType
  sku?: string
  barcode?: string
  brand?: string
  model?: string
  images?: string[]
  base_price: number
  sale_price?: number
  cost_price?: number
  weight?: number
  dimensions?: {
    length?: number
    width?: number
    height?: number
    unit?: string
  }
  attributes?: ProductAttribute[]
  specifications?: ProductSpecification[]
  tags?: string[]
  is_featured?: boolean
  is_digital?: boolean
  requires_shipping?: boolean
  is_active?: boolean
  meta_title?: string
  meta_description?: string
  seo_keywords?: string[]
}

// 商品变体表单数据类型
export interface ProductVariantFormData {
  product_id: string
  name: string
  sku?: string
  barcode?: string
  price: number
  cost_price?: number
  weight?: number
  images?: string[]
  attributes?: Record<string, string>
  is_active?: boolean
}

// 商品分类表单数据类型
export interface ProductCategoryFormData {
  name: string
  description?: string
  parent_id?: string
  image?: string
  sort_order?: number
  is_active?: boolean
  meta_title?: string
  meta_description?: string
}

// 库存管理表单数据类型
export interface StockFormData {
  product_id: string
  variant_id?: string
  quantity: number
  operation: 'add' | 'subtract' | 'set'
  reason?: string
  location?: string
  batch_number?: string
  expiry_date?: string
  notes?: string
}

// 商品搜索参数类型
export interface ProductSearchParams {
  query?: string
  category_id?: string
  product_type?: ProductType[]
  status?: ProductStatus[]
  stock_status?: StockStatus[]
  min_price?: number
  max_price?: number
  brand?: string
  tags?: string[]
  is_featured?: boolean
  is_digital?: boolean
  sort_by?: 'name' | 'price' | 'created_at' | 'sales' | 'rating'
  sort_order?: 'asc' | 'desc'
  page?: number
  limit?: number
}

// 商品统计信息类型
export interface ProductStats {
  total_products: number
  active_products: number
  out_of_stock_products: number
  low_stock_products: number
  total_categories: number
  total_variants: number
  total_inventory_value: number
  best_selling_products: ProductWithDetails[]
  low_stock_alerts: ProductWithDetails[]
  recent_products: ProductWithDetails[]
}

// 商品操作结果类型
export interface ProductActionResult {
  success: boolean
  data?: Product | ProductCategory | ProductVariant
  error?: string
  message?: string
}

// 商品状态配置
export const PRODUCT_STATUS_CONFIG = {
  active: {
    label: '上架',
    description: '商品正常销售',
    color: 'green',
    icon: '✅',
  },
  inactive: {
    label: '下架',
    description: '商品暂停销售',
    color: 'gray',
    icon: '⏸️',
  },
  out_of_stock: {
    label: '缺货',
    description: '商品库存不足',
    color: 'red',
    icon: '📦',
  },
  discontinued: {
    label: '停产',
    description: '商品已停产',
    color: 'red',
    icon: '🚫',
  },
} as const

// 商品类型配置
export const PRODUCT_TYPE_CONFIG = {
  physical: {
    label: '实体商品',
    description: '需要物流配送的实体商品',
    icon: '📦',
    requires_shipping: true,
  },
  digital: {
    label: '数字商品',
    description: '可下载的数字商品',
    icon: '💾',
    requires_shipping: false,
  },
  service: {
    label: '服务',
    description: '提供的服务项目',
    icon: '🛠️',
    requires_shipping: false,
  },
  subscription: {
    label: '订阅',
    description: '定期付费的订阅服务',
    icon: '🔄',
    requires_shipping: false,
  },
  gift_card: {
    label: '礼品卡',
    description: '可用于购买的礼品卡',
    icon: '🎁',
    requires_shipping: false,
  },
  bundle: {
    label: '套装',
    description: '多个商品的组合套装',
    icon: '📋',
    requires_shipping: true,
  },
} as const

// 商品分类配置
export const PRODUCT_CATEGORY_CONFIG = {
  electronics: { label: '电子产品', icon: '📱', color: 'blue' },
  clothing: { label: '服装', icon: '👕', color: 'purple' },
  food: { label: '食品', icon: '🍎', color: 'green' },
  books: { label: '图书', icon: '📚', color: 'brown' },
  home: { label: '家居', icon: '🏠', color: 'orange' },
  beauty: { label: '美妆', icon: '💄', color: 'pink' },
  sports: { label: '运动', icon: '⚽', color: 'red' },
  toys: { label: '玩具', icon: '🧸', color: 'yellow' },
  automotive: { label: '汽车', icon: '🚗', color: 'gray' },
  health: { label: '健康', icon: '💊', color: 'green' },
  jewelry: { label: '珠宝', icon: '💎', color: 'purple' },
  art: { label: '艺术', icon: '🎨', color: 'rainbow' },
  music: { label: '音乐', icon: '🎵', color: 'blue' },
  software: { label: '软件', icon: '💻', color: 'blue' },
  services: { label: '服务', icon: '🛠️', color: 'gray' },
  other: { label: '其他', icon: '📦', color: 'gray' },
} as const

// 库存状态配置
export const STOCK_STATUS_CONFIG = {
  in_stock: {
    label: '有库存',
    description: '商品库存充足',
    color: 'green',
    icon: '✅',
  },
  low_stock: {
    label: '库存不足',
    description: '商品库存较少',
    color: 'yellow',
    icon: '⚠️',
  },
  out_of_stock: {
    label: '缺货',
    description: '商品暂时缺货',
    color: 'red',
    icon: '❌',
  },
  pre_order: {
    label: '预订',
    description: '商品可预订',
    color: 'blue',
    icon: '📅',
  },
} as const

// 默认商品属性
export const DEFAULT_PRODUCT_ATTRIBUTES: ProductAttribute[] = [
  { name: '颜色', value: '', type: 'color', required: false },
  { name: '尺寸', value: '', type: 'select', options: ['XS', 'S', 'M', 'L', 'XL', 'XXL'], required: false },
  { name: '材质', value: '', type: 'text', required: false },
  { name: '产地', value: '', type: 'text', required: false },
]

// 库存操作类型
export const STOCK_OPERATIONS = {
  add: { label: '入库', description: '增加库存数量', icon: '➕' },
  subtract: { label: '出库', description: '减少库存数量', icon: '➖' },
  set: { label: '设置', description: '设置库存数量', icon: '🔢' },
} as const

// 价格计算工具函数
export const calculateDiscountedPrice = (basePrice: number, discountPercentage: number): number => {
  return Math.round((basePrice * (1 - discountPercentage / 100)) * 100) / 100
}

export const calculateTaxAmount = (price: number, taxRate: number): number => {
  return Math.round((price * taxRate / 100) * 100) / 100
}

export const calculateFinalPrice = (basePrice: number, discountPercentage: number = 0, taxRate: number = 0): number => {
  const discountedPrice = calculateDiscountedPrice(basePrice, discountPercentage)
  const taxAmount = calculateTaxAmount(discountedPrice, taxRate)
  return discountedPrice + taxAmount
}
