/**
 * 移动端表格组件
 * 将传统表格转换为移动端友好的卡片列表
 */

'use client'

import { useState } from 'react'
import { ChevronDown, ChevronRight, MoreVertical, Filter, Search } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { 
  Collapsible, 
  CollapsibleContent, 
  CollapsibleTrigger 
} from '@/components/ui/collapsible'

// 表格列定义
export interface MobileTableColumn {
  key: string
  label: string
  type?: 'text' | 'number' | 'currency' | 'date' | 'badge' | 'action'
  primary?: boolean // 是否为主要显示字段
  secondary?: boolean // 是否为次要显示字段
  hidden?: boolean // 是否在移动端隐藏
  render?: (value: any, row: any) => React.ReactNode
  sortable?: boolean
  filterable?: boolean
}

// 表格数据行
export interface MobileTableRow {
  id: string | number
  [key: string]: any
}

// 表格属性
interface MobileTableProps {
  columns: MobileTableColumn[]
  data: MobileTableRow[]
  loading?: boolean
  searchable?: boolean
  filterable?: boolean
  sortable?: boolean
  expandable?: boolean
  onRowClick?: (row: MobileTableRow) => void
  onRowAction?: (action: string, row: MobileTableRow) => void
  emptyMessage?: string
  className?: string
}

export function MobileTable({
  columns,
  data,
  loading = false,
  searchable = true,
  filterable = false,
  sortable = false,
  expandable = false,
  onRowClick,
  onRowAction,
  emptyMessage = '暂无数据',
  className = ''
}: MobileTableProps) {
  const [searchQuery, setSearchQuery] = useState('')
  const [sortField, setSortField] = useState<string>('')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [expandedRows, setExpandedRows] = useState<Set<string | number>>(new Set())

  // 获取主要显示列
  const primaryColumns = columns.filter(col => col.primary && !col.hidden)
  const secondaryColumns = columns.filter(col => col.secondary && !col.hidden)
  const detailColumns = columns.filter(col => !col.primary && !col.secondary && !col.hidden)

  // 过滤和排序数据
  const filteredData = data.filter(row => {
    if (!searchQuery) return true
    
    return columns.some(col => {
      const value = row[col.key]
      if (value == null) return false
      return String(value).toLowerCase().includes(searchQuery.toLowerCase())
    })
  })

  const sortedData = [...filteredData].sort((a, b) => {
    if (!sortField) return 0
    
    const aValue = a[sortField]
    const bValue = b[sortField]
    
    if (aValue == null && bValue == null) return 0
    if (aValue == null) return 1
    if (bValue == null) return -1
    
    const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0
    return sortOrder === 'asc' ? comparison : -comparison
  })

  // 格式化值
  const formatValue = (value: any, column: MobileTableColumn) => {
    if (value == null) return '-'
    
    switch (column.type) {
      case 'currency':
        return `¥${Number(value).toLocaleString()}`
      case 'date':
        return new Date(value).toLocaleDateString('zh-CN')
      case 'number':
        return Number(value).toLocaleString()
      case 'badge':
        return <Badge variant="outline">{value}</Badge>
      default:
        return String(value)
    }
  }

  // 渲染值
  const renderValue = (value: any, column: MobileTableColumn, row: MobileTableRow) => {
    if (column.render) {
      return column.render(value, row)
    }
    return formatValue(value, column)
  }

  // 切换行展开状态
  const toggleRowExpansion = (rowId: string | number) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(rowId)) {
      newExpanded.delete(rowId)
    } else {
      newExpanded.add(rowId)
    }
    setExpandedRows(newExpanded)
  }

  // 处理排序
  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortField(field)
      setSortOrder('asc')
    }
  }

  if (loading) {
    return (
      <div className={`space-y-3 ${className}`}>
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
                <div className="h-3 bg-muted rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 搜索和筛选栏 */}
      {(searchable || filterable || sortable) && (
        <div className="flex items-center space-x-2">
          {searchable && (
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="搜索..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>
          )}
          
          {filterable && (
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4" />
            </Button>
          )}
          
          {sortable && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  排序
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                {columns.filter(col => col.sortable).map(col => (
                  <DropdownMenuItem
                    key={col.key}
                    onClick={() => handleSort(col.key)}
                  >
                    {col.label}
                    {sortField === col.key && (
                      <span className="ml-2">
                        {sortOrder === 'asc' ? '↑' : '↓'}
                      </span>
                    )}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      )}

      {/* 数据列表 */}
      {sortedData.length === 0 ? (
        <Card>
          <CardContent className="flex items-center justify-center h-32">
            <p className="text-muted-foreground">{emptyMessage}</p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-3">
          {sortedData.map((row) => {
            const isExpanded = expandedRows.has(row.id)
            
            return (
              <Card key={row.id} className="overflow-hidden">
                <CardContent className="p-0">
                  <Collapsible open={isExpanded} onOpenChange={() => toggleRowExpansion(row.id)}>
                    {/* 主要内容区域 */}
                    <div 
                      className={`p-4 ${onRowClick ? 'cursor-pointer hover:bg-muted/50' : ''}`}
                      onClick={() => onRowClick?.(row)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1 space-y-2">
                          {/* 主要字段 */}
                          {primaryColumns.map(col => (
                            <div key={col.key} className="flex items-center space-x-2">
                              <span className="font-medium">
                                {renderValue(row[col.key], col, row)}
                              </span>
                            </div>
                          ))}
                          
                          {/* 次要字段 */}
                          {secondaryColumns.length > 0 && (
                            <div className="flex flex-wrap gap-2">
                              {secondaryColumns.map(col => (
                                <div key={col.key} className="text-sm text-muted-foreground">
                                  <span className="font-medium">{col.label}:</span>
                                  <span className="ml-1">
                                    {renderValue(row[col.key], col, row)}
                                  </span>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>

                        {/* 操作按钮 */}
                        <div className="flex items-center space-x-2 ml-4">
                          {expandable && detailColumns.length > 0 && (
                            <CollapsibleTrigger asChild>
                              <Button variant="ghost" size="sm" className="p-1">
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                              </Button>
                            </CollapsibleTrigger>
                          )}
                          
                          {onRowAction && (
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="p-1">
                                  <MoreVertical className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => onRowAction('view', row)}>
                                  查看详情
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => onRowAction('edit', row)}>
                                  编辑
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => onRowAction('delete', row)}
                                  className="text-destructive"
                                >
                                  删除
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* 展开的详细信息 */}
                    {expandable && detailColumns.length > 0 && (
                      <CollapsibleContent>
                        <div className="border-t bg-muted/20 p-4 space-y-3">
                          {detailColumns.map(col => (
                            <div key={col.key} className="flex justify-between items-center">
                              <span className="text-sm font-medium text-muted-foreground">
                                {col.label}
                              </span>
                              <span className="text-sm">
                                {renderValue(row[col.key], col, row)}
                              </span>
                            </div>
                          ))}
                        </div>
                      </CollapsibleContent>
                    )}
                  </Collapsible>
                </CardContent>
              </Card>
            )
          })}
        </div>
      )}
    </div>
  )
}

// 移动端表格的简化版本
interface SimpleMobileTableProps {
  title: string
  data: Array<{
    id: string | number
    title: string
    subtitle?: string
    description?: string
    badge?: React.ReactNode
    action?: React.ReactNode
  }>
  onItemClick?: (item: any) => void
  className?: string
}

export function SimpleMobileTable({ 
  title, 
  data, 
  onItemClick, 
  className = '' 
}: SimpleMobileTableProps) {
  return (
    <Card className={className}>
      <CardContent className="p-0">
        <div className="p-4 border-b">
          <h3 className="font-semibold">{title}</h3>
        </div>
        
        <div className="divide-y">
          {data.map((item) => (
            <div
              key={item.id}
              className={`p-4 ${onItemClick ? 'cursor-pointer hover:bg-muted/50' : ''}`}
              onClick={() => onItemClick?.(item)}
            >
              <div className="flex items-center justify-between">
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium truncate">{item.title}</h4>
                  {item.subtitle && (
                    <p className="text-sm text-muted-foreground truncate">
                      {item.subtitle}
                    </p>
                  )}
                  {item.description && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {item.description}
                    </p>
                  )}
                </div>
                
                <div className="flex items-center space-x-2 ml-4">
                  {item.badge}
                  {item.action}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
