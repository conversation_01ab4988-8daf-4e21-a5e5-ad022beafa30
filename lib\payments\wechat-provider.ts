/**
 * 微信支付实现
 * 集成微信支付API
 */

import crypto from 'crypto'
import type {
  PaymentProvider,
  CreatePaymentParams,
  PaymentResult,
  PaymentQueryResult,
  CallbackVerifyResult
} from '@/types/payment'

export class WechatPayProvider implements PaymentProvider {
  name = 'wechat_pay'
  displayName = '微信支付'
  icon = '🟢'
  isEnabled = false // 默认关闭，需要配置后开启
  
  config = {
    appId: process.env.WECHAT_APP_ID || 'wx**********123456',
    mchId: process.env.WECHAT_MCH_ID || '**********',
    apiKey: process.env.WECHAT_API_KEY || '',
    certPath: process.env.WECHAT_CERT_PATH || '',
    keyPath: process.env.WECHAT_KEY_PATH || '',
    notifyUrl: process.env.WECHAT_NOTIFY_URL || 'https://your-domain.com/api/payments/wechat/notify',
    apiUrl: process.env.WECHAT_API_URL || 'https://api.mch.weixin.qq.com',
  }

  // 创建支付
  async createPayment(params: CreatePaymentParams): Promise<PaymentResult> {
    try {
      const outTradeNo = this.generateTradeNo()
      
      // 构建支付参数
      const paymentParams = {
        appid: this.config.appId,
        mch_id: this.config.mchId,
        nonce_str: this.generateNonceStr(),
        body: params.description,
        out_trade_no: outTradeNo,
        total_fee: Math.round(params.amount * 100), // 微信支付金额单位为分
        spbill_create_ip: '127.0.0.1',
        notify_url: params.notifyUrl || this.config.notifyUrl,
        trade_type: 'NATIVE', // 扫码支付
      }

      // 生成签名
      const sign = this.generateSign(paymentParams)
      const signedParams = { ...paymentParams, sign }

      // 构建XML请求
      const xmlData = this.buildXmlRequest(signedParams)

      // 发送统一下单请求
      const response = await this.makeApiRequest('/pay/unifiedorder', xmlData)

      if (response.return_code === 'SUCCESS' && response.result_code === 'SUCCESS') {
        // 生成二维码URL
        const qrCode = await this.generateQRCode(response.code_url)

        return {
          success: true,
          paymentId: outTradeNo,
          paymentUrl: response.code_url,
          qrCode,
          message: '微信支付订单创建成功',
          data: response,
        }
      } else {
        return {
          success: false,
          error: response.err_code_des || response.return_msg || '创建微信支付失败',
        }
      }
    } catch (error) {
      console.error('Wechat pay create payment error:', error)
      return {
        success: false,
        error: '创建微信支付失败',
      }
    }
  }

  // 查询支付状态
  async queryPayment(paymentId: string): Promise<PaymentQueryResult> {
    try {
      const queryParams = {
        appid: this.config.appId,
        mch_id: this.config.mchId,
        out_trade_no: paymentId,
        nonce_str: this.generateNonceStr(),
      }

      const sign = this.generateSign(queryParams)
      const signedParams = { ...queryParams, sign }

      const xmlData = this.buildXmlRequest(signedParams)
      const response = await this.makeApiRequest('/pay/orderquery', xmlData)

      if (response.return_code === 'SUCCESS' && response.result_code === 'SUCCESS') {
        const status = this.mapTradeState(response.trade_state)
        
        return {
          success: true,
          status,
          amount: parseInt(response.total_fee || '0') / 100,
          paidAmount: parseInt(response.cash_fee || '0') / 100,
          paidAt: response.time_end,
          data: response,
        }
      } else {
        return {
          success: false,
          status: 'failed',
          error: response.err_code_des || response.return_msg || '查询支付状态失败',
        }
      }
    } catch (error) {
      console.error('Wechat pay query payment error:', error)
      return {
        success: false,
        status: 'failed',
        error: '查询微信支付状态失败',
      }
    }
  }

  // 取消支付
  async cancelPayment(paymentId: string): Promise<PaymentResult> {
    try {
      const cancelParams = {
        appid: this.config.appId,
        mch_id: this.config.mchId,
        out_trade_no: paymentId,
        nonce_str: this.generateNonceStr(),
      }

      const sign = this.generateSign(cancelParams)
      const signedParams = { ...cancelParams, sign }

      const xmlData = this.buildXmlRequest(signedParams)
      const response = await this.makeApiRequest('/pay/closeorder', xmlData)

      if (response.return_code === 'SUCCESS' && response.result_code === 'SUCCESS') {
        return {
          success: true,
          message: '微信支付已取消',
          data: response,
        }
      } else {
        return {
          success: false,
          error: response.err_code_des || response.return_msg || '取消微信支付失败',
        }
      }
    } catch (error) {
      console.error('Wechat pay cancel payment error:', error)
      return {
        success: false,
        error: '取消微信支付失败',
      }
    }
  }

  // 退款
  async refundPayment(paymentId: string, amount?: number): Promise<PaymentResult> {
    try {
      const refundNo = this.generateRefundNo()
      
      // 需要先查询原订单信息获取总金额
      const queryResult = await this.queryPayment(paymentId)
      if (!queryResult.success) {
        return {
          success: false,
          error: '查询原订单失败，无法退款',
        }
      }

      const totalFee = Math.round((queryResult.amount || 0) * 100)
      const refundFee = amount ? Math.round(amount * 100) : totalFee

      const refundParams = {
        appid: this.config.appId,
        mch_id: this.config.mchId,
        nonce_str: this.generateNonceStr(),
        out_trade_no: paymentId,
        out_refund_no: refundNo,
        total_fee: totalFee,
        refund_fee: refundFee,
        refund_desc: '用户申请退款',
      }

      const sign = this.generateSign(refundParams)
      const signedParams = { ...refundParams, sign }

      const xmlData = this.buildXmlRequest(signedParams)
      const response = await this.makeApiRequest('/secapi/pay/refund', xmlData)

      if (response.return_code === 'SUCCESS' && response.result_code === 'SUCCESS') {
        return {
          success: true,
          paymentId: refundNo,
          message: '微信退款申请成功',
          data: response,
        }
      } else {
        return {
          success: false,
          error: response.err_code_des || response.return_msg || '微信退款失败',
        }
      }
    } catch (error) {
      console.error('Wechat pay refund payment error:', error)
      return {
        success: false,
        error: '微信退款失败',
      }
    }
  }

  // 验证回调
  async verifyCallback(data: any): Promise<CallbackVerifyResult> {
    try {
      const { sign, ...params } = data

      // 验证签名
      const expectedSign = this.generateSign(params)
      if (sign !== expectedSign) {
        return {
          success: false,
          error: '微信支付回调签名验证失败',
        }
      }

      // 验证商户号
      if (params.mch_id !== this.config.mchId) {
        return {
          success: false,
          error: '商户号不匹配',
        }
      }

      const status = this.mapTradeState(params.result_code === 'SUCCESS' ? 'SUCCESS' : 'FAIL')

      return {
        success: true,
        paymentId: params.out_trade_no,
        status,
        amount: parseInt(params.total_fee || '0') / 100,
        data: params,
      }
    } catch (error) {
      console.error('Wechat pay verify callback error:', error)
      return {
        success: false,
        error: '验证微信支付回调失败',
      }
    }
  }

  // 生成签名
  private generateSign(params: Record<string, any>): string {
    // 排除sign字段，按key排序
    const sortedParams = Object.keys(params)
      .filter(key => key !== 'sign' && params[key] !== undefined && params[key] !== null && params[key] !== '')
      .sort()
      .map(key => `${key}=${params[key]}`)
      .join('&')

    // 添加API密钥
    const stringToSign = `${sortedParams}&key=${this.config.apiKey}`

    // 计算MD5签名并转为大写
    return crypto.createHash('md5').update(stringToSign, 'utf8').digest('hex').toUpperCase()
  }

  // 映射交易状态
  private mapTradeState(tradeState: string): 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded' {
    const statusMap: Record<string, any> = {
      'NOTPAY': 'pending',
      'USERPAYING': 'processing',
      'SUCCESS': 'completed',
      'FAIL': 'failed',
      'CLOSED': 'cancelled',
      'REVOKED': 'cancelled',
      'REFUND': 'refunded',
    }
    
    return statusMap[tradeState] || 'pending'
  }

  // 生成随机字符串
  private generateNonceStr(): string {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
  }

  // 生成交易号
  private generateTradeNo(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    return `WX${timestamp}${random}`
  }

  // 生成退款号
  private generateRefundNo(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    return `WXRF${timestamp}${random}`
  }

  // 构建XML请求
  private buildXmlRequest(params: Record<string, any>): string {
    const xmlParts = ['<xml>']
    
    Object.keys(params).forEach(key => {
      xmlParts.push(`<${key}><![CDATA[${params[key]}]]></${key}>`)
    })
    
    xmlParts.push('</xml>')
    return xmlParts.join('')
  }

  // 发送API请求
  private async makeApiRequest(endpoint: string, xmlData: string): Promise<any> {
    // 模拟API响应（实际项目中应该发送真实的HTTP请求）
    return new Promise((resolve) => {
      setTimeout(() => {
        // 模拟成功响应
        if (endpoint === '/pay/unifiedorder') {
          resolve({
            return_code: 'SUCCESS',
            return_msg: 'OK',
            result_code: 'SUCCESS',
            prepay_id: 'wx******************************',
            code_url: 'weixin://wxpay/bizpayurl?pr=mock_code_url',
          })
        } else if (endpoint === '/pay/orderquery') {
          resolve({
            return_code: 'SUCCESS',
            return_msg: 'OK',
            result_code: 'SUCCESS',
            trade_state: 'NOTPAY',
            total_fee: '10000',
            cash_fee: '0',
          })
        } else {
          resolve({
            return_code: 'SUCCESS',
            return_msg: 'OK',
            result_code: 'SUCCESS',
          })
        }
      }, 1000)
    })
  }

  // 生成二维码
  private async generateQRCode(codeUrl: string): Promise<string> {
    // 模拟二维码生成（实际项目中应该使用真实的二维码生成库）
    return `data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==`
  }
}
